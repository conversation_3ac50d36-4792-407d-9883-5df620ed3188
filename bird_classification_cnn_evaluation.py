#!/usr/bin/env python3
"""
Bird Classification CNN - Evaluation and Deployment Script
Script untuk evaluasi model dan deployment yang melengkapi notebook utama
"""

import os
import json
import numpy as np
import pandas as pd
import tensorflow as tf
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import accuracy_score, confusion_matrix, classification_report
from tensorflow.keras.preprocessing import image

def evaluate_model(model_path, data_dir, results_dir='./results'):
    """
    Evaluasi model yang sudah ditraining
    """
    print("📊 Starting model evaluation...")
    
    # Create results directory
    os.makedirs(results_dir, exist_ok=True)
    
    # Load model
    print(f"📂 Loading model from: {model_path}")
    model = tf.keras.models.load_model(model_path)
    
    # Setup data generator for evaluation
    from tensorflow.keras.preprocessing.image import ImageDataGenerator
    
    eval_datagen = ImageDataGenerator(rescale=1./255)
    
    eval_generator = eval_datagen.flow_from_directory(
        data_dir,
        target_size=(224, 224),
        batch_size=16,
        class_mode='categorical',
        shuffle=False
    )
    
    # Evaluate model
    print("🔍 Evaluating model...")
    loss, accuracy = model.evaluate(eval_generator, verbose=1)
    
    # Generate predictions
    print("🔮 Generating predictions...")
    predictions = model.predict(eval_generator, verbose=1)
    predicted_classes = np.argmax(predictions, axis=1)
    true_classes = eval_generator.classes
    class_labels = list(eval_generator.class_indices.keys())
    
    # Calculate metrics
    overall_accuracy = accuracy_score(true_classes, predicted_classes)
    conf_matrix = confusion_matrix(true_classes, predicted_classes)
    class_report = classification_report(true_classes, predicted_classes, 
                                       target_names=class_labels, digits=4)
    
    print(f"\n🎯 Evaluation Results:")
    print(f"   Loss: {loss:.4f}")
    print(f"   Accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)")
    print(f"   Overall Accuracy: {overall_accuracy:.4f} ({overall_accuracy*100:.2f}%)")
    
    # Plot confusion matrix
    plt.figure(figsize=(10, 8))
    sns.heatmap(conf_matrix, annot=True, fmt='d', cmap='Blues',
                xticklabels=class_labels, yticklabels=class_labels,
                cbar_kws={'label': 'Count'})
    plt.title('🔍 Confusion Matrix - Bird Classification', fontsize=16, fontweight='bold')
    plt.xlabel('Predicted Label', fontsize=12)
    plt.ylabel('True Label', fontsize=12)
    plt.xticks(rotation=45, ha='right')
    plt.yticks(rotation=0)
    plt.tight_layout()
    
    # Save confusion matrix
    confusion_path = os.path.join(results_dir, 'confusion_matrix.png')
    plt.savefig(confusion_path, dpi=300, bbox_inches='tight')
    plt.show()
    
    # Save classification report
    report_path = os.path.join(results_dir, 'classification_report.txt')
    with open(report_path, 'w') as f:
        f.write("Bird Classification - Custom CNN Evaluation\n")
        f.write("="*50 + "\n\n")
        f.write(f"Model: {model_path}\n")
        f.write(f"Data: {data_dir}\n\n")
        f.write(f"Loss: {loss:.4f}\n")
        f.write(f"Accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)\n")
        f.write(f"Overall Accuracy: {overall_accuracy:.4f} ({overall_accuracy*100:.2f}%)\n\n")
        f.write("Classification Report:\n")
        f.write(class_report)
    
    print(f"✅ Evaluation results saved to: {results_dir}")
    
    return {
        'loss': loss,
        'accuracy': accuracy,
        'overall_accuracy': overall_accuracy,
        'confusion_matrix': conf_matrix,
        'class_labels': class_labels
    }

def visualize_sample_predictions(model_path, data_dir, results_dir='./results', n_samples=12):
    """
    Visualisasi prediksi sample gambar
    """
    print("🖼️ Creating sample predictions visualization...")
    
    # Load model
    model = tf.keras.models.load_model(model_path)
    
    # Get class names
    class_names = [d for d in os.listdir(data_dir) 
                   if os.path.isdir(os.path.join(data_dir, d))]
    
    fig, axes = plt.subplots(3, 4, figsize=(16, 12))
    axes = axes.ravel()
    
    sample_count = 0
    
    for class_name in class_names:
        class_path = os.path.join(data_dir, class_name)
        if os.path.exists(class_path):
            image_files = [f for f in os.listdir(class_path) 
                          if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff'))][:3]
            
            for img_file in image_files:
                if sample_count >= n_samples:
                    break
                    
                img_path = os.path.join(class_path, img_file)
                
                # Load and preprocess image
                img = image.load_img(img_path, target_size=(224, 224))
                img_array = image.img_to_array(img) / 255.0
                img_array = np.expand_dims(img_array, axis=0)
                
                # Make prediction
                prediction = model.predict(img_array, verbose=0)
                predicted_class_idx = np.argmax(prediction)
                predicted_class = class_names[predicted_class_idx]
                confidence = np.max(prediction) * 100
                
                # Display image
                axes[sample_count].imshow(img)
                
                # Create title with prediction info
                title_color = 'green' if predicted_class == class_name else 'red'
                title = f"True: {class_name}\nPred: {predicted_class}\nConf: {confidence:.1f}%"
                axes[sample_count].set_title(title, fontsize=10, color=title_color, fontweight='bold')
                axes[sample_count].axis('off')
                
                sample_count += 1
                
        if sample_count >= n_samples:
            break
    
    # Hide unused subplots
    for i in range(sample_count, len(axes)):
        axes[i].axis('off')
    
    plt.suptitle('🐦 Sample Predictions - Bird Classification', fontsize=16, fontweight='bold')
    plt.tight_layout()
    
    # Save visualization
    pred_path = os.path.join(results_dir, 'sample_predictions.png')
    plt.savefig(pred_path, dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"✅ Sample predictions saved to: {pred_path}")

def convert_to_tflite(model_path, output_dir='./models'):
    """
    Convert model ke TensorFlow Lite untuk mobile deployment
    """
    print("📱 Converting to TensorFlow Lite...")
    
    # Load model
    model = tf.keras.models.load_model(model_path)
    
    # Convert to TensorFlow Lite
    converter = tf.lite.TFLiteConverter.from_keras_model(model)
    converter.optimizations = [tf.lite.Optimize.DEFAULT]
    
    tflite_model = converter.convert()
    
    # Save TFLite model
    tflite_path = os.path.join(output_dir, 'bird_classification_cnn.tflite')
    with open(tflite_path, 'wb') as f:
        f.write(tflite_model)
    
    # Model size comparison
    h5_size = os.path.getsize(model_path) / (1024 * 1024)  # MB
    tflite_size = os.path.getsize(tflite_path) / (1024 * 1024)  # MB
    
    print(f"📱 TensorFlow Lite model saved to: {tflite_path}")
    print(f"📊 Model Size Comparison:")
    print(f"   H5 Model: {h5_size:.2f} MB")
    print(f"   TFLite Model: {tflite_size:.2f} MB")
    print(f"   Size Reduction: {((h5_size - tflite_size) / h5_size * 100):.1f}%")
    
    return tflite_path, h5_size, tflite_size

def create_model_metadata(model_path, data_dir, eval_results, h5_size, tflite_size, output_dir='./models'):
    """
    Buat metadata model untuk deployment
    """
    print("📋 Creating model metadata...")
    
    # Load model untuk info parameter
    model = tf.keras.models.load_model(model_path)
    
    # Get class names
    class_names = [d for d in os.listdir(data_dir) 
                   if os.path.isdir(os.path.join(data_dir, d))]
    
    metadata = {
        'model_name': 'Bird Classification Custom CNN',
        'model_type': 'Custom CNN',
        'framework': 'TensorFlow/Keras',
        'classes': class_names,
        'num_classes': len(class_names),
        'input_shape': [224, 224, 3],
        'total_parameters': int(model.count_params()),
        'model_size_mb': float(h5_size),
        'tflite_size_mb': float(tflite_size),
        'evaluation_metrics': {
            'loss': float(eval_results['loss']),
            'accuracy': float(eval_results['accuracy']),
            'overall_accuracy': float(eval_results['overall_accuracy'])
        },
        'preprocessing': {
            'input_size': [224, 224],
            'normalization': 'rescale 1/255',
            'color_mode': 'rgb'
        },
        'deployment_info': {
            'mobile_ready': True,
            'tflite_available': True,
            'recommended_use': 'Bird species classification'
        }
    }
    
    metadata_path = os.path.join(output_dir, 'model_metadata.json')
    with open(metadata_path, 'w') as f:
        json.dump(metadata, f, indent=2)
    
    print(f"📋 Model metadata saved to: {metadata_path}")
    return metadata_path

def main():
    """
    Main function untuk menjalankan evaluasi lengkap
    """
    print("🚀 Starting Bird Classification CNN Evaluation")
    print("="*60)
    
    # Configuration
    model_path = './models/best_bird_cnn_model.h5'
    data_dir = './ResizedDataset'  # Sesuaikan dengan dataset Anda
    results_dir = './results'
    models_dir = './models'
    
    # Create directories
    os.makedirs(results_dir, exist_ok=True)
    os.makedirs(models_dir, exist_ok=True)
    
    # Check if model exists
    if not os.path.exists(model_path):
        print(f"❌ Model not found: {model_path}")
        print("Please train the model first using the notebook.")
        return
    
    # Check if data directory exists
    if not os.path.exists(data_dir):
        print(f"❌ Data directory not found: {data_dir}")
        print("Please check your data directory path.")
        return
    
    try:
        # 1. Evaluate model
        eval_results = evaluate_model(model_path, data_dir, results_dir)
        
        # 2. Visualize sample predictions
        visualize_sample_predictions(model_path, data_dir, results_dir)
        
        # 3. Convert to TensorFlow Lite
        tflite_path, h5_size, tflite_size = convert_to_tflite(model_path, models_dir)
        
        # 4. Create model metadata
        metadata_path = create_model_metadata(model_path, data_dir, eval_results, 
                                            h5_size, tflite_size, models_dir)
        
        # 5. Summary
        print("\n" + "="*60)
        print("🎉 EVALUATION COMPLETED SUCCESSFULLY!")
        print("="*60)
        print(f"📊 Accuracy: {eval_results['accuracy']*100:.2f}%")
        print(f"📁 Results saved to: {results_dir}")
        print(f"🤖 Models saved to: {models_dir}")
        print(f"📱 TFLite model: {tflite_path}")
        print(f"📋 Metadata: {metadata_path}")
        print("="*60)
        
    except Exception as e:
        print(f"❌ Error during evaluation: {str(e)}")
        print("Please check your model and data paths.")

if __name__ == "__main__":
    main()
