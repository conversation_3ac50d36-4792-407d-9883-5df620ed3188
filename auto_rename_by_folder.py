#!/usr/bin/env python3
"""
Auto Rename by Folder Name
Script untuk rename file otomatis berdasarkan nama folder masing-masing.
Setiap folder akan menggunakan namanya sendiri sebagai base name.

Contoh:
- Folder "Anisolabididae" → file jadi "Anisolabididae 1.jpg", "Anisolabididae 2.png", dst.
- Folder "Carabidae" → file jadi "Carabidae 1.jpg", "Carabidae 2.png", dst.
"""

import os
from pathlib import Path
import time

def rename_folder_by_name(folder_path, start_number=1, preview=False):
    """
    Rename semua file dalam folder menggunakan nama folder sebagai base name
    
    Args:
        folder_path (str): Path ke folder
        start_number (int): Nomor awal
        preview (bool): Preview mode
    
    Returns:
        tuple: (success_count, failed_count)
    """
    
    folder = Path(folder_path)
    
    if not folder.exists():
        print(f"❌ Folder '{folder_path}' tidak ditemukan!")
        return 0, 0
    
    if not folder.is_dir():
        print(f"❌ '{folder_path}' bukan folder!")
        return 0, 0
    
    # Dapatkan nama folder sebagai base name
    base_name = folder.name
    
    # Dapatkan semua file
    files = [f for f in folder.iterdir() if f.is_file()]
    
    if not files:
        print(f"📁 Folder '{base_name}' kosong!")
        return 0, 0
    
    # Urutkan file
    files.sort(key=lambda x: x.name.lower())
    
    print(f"📁 Folder: {folder}")
    print(f"🏷️  Base name: {base_name}")
    print(f"📊 Total file: {len(files)}")
    print(f"🔢 Start number: {start_number}")
    
    if preview:
        print(f"\n🔍 PREVIEW MODE:")
        print("-" * 50)
    else:
        print(f"\n🔄 Memulai rename:")
        print("-" * 50)
    
    success_count = 0
    failed_count = 0
    
    for i, file_path in enumerate(files, start_number):
        try:
            # Dapatkan ekstensi
            extension = file_path.suffix
            
            # Nama baru menggunakan nama folder
            new_name = f"{base_name} {i}{extension}"
            new_path = folder / new_name
            
            if preview:
                print(f"  {file_path.name} → {new_name}")
                success_count += 1
            else:
                # Skip jika sudah sesuai
                if file_path.name == new_name:
                    print(f"  ⏭️  {file_path.name} (sudah sesuai)")
                    success_count += 1
                    continue
                
                # Cek konflik
                if new_path.exists():
                    print(f"  ❌ {file_path.name} → KONFLIK: {new_name} sudah ada")
                    failed_count += 1
                    continue
                
                # Rename
                file_path.rename(new_path)
                print(f"  ✅ {file_path.name} → {new_name}")
                success_count += 1
                
        except Exception as e:
            print(f"  ❌ ERROR: {file_path.name} - {e}")
            failed_count += 1
    
    print("-" * 50)
    if preview:
        print(f"📋 PREVIEW: {len(files)} file akan direname")
    else:
        print(f"📋 SELESAI: ✅{success_count} ❌{failed_count}")
    
    return success_count, failed_count

def auto_rename_multiple_folders(base_directory, start_number=1, preview=False):
    """
    Auto rename semua folder dalam directory
    Setiap folder menggunakan namanya sendiri sebagai base name
    """
    
    base_path = Path(base_directory)
    
    if not base_path.exists():
        print(f"❌ Directory '{base_directory}' tidak ditemukan!")
        return
    
    # Dapatkan semua subfolder
    subfolders = [f for f in base_path.iterdir() if f.is_dir()]
    
    if not subfolders:
        print(f"📁 Tidak ada subfolder dalam '{base_directory}'!")
        return
    
    # Urutkan folder
    subfolders.sort(key=lambda x: x.name.lower())
    
    print(f"🔄 AUTO RENAME BY FOLDER NAME")
    print("=" * 60)
    print(f"📁 Base directory: {base_path}")
    print(f"📊 Total folder: {len(subfolders)}")
    print(f"🔢 Start number: {start_number}")
    
    if preview:
        print(f"🔍 MODE: PREVIEW")
    else:
        print(f"🔄 MODE: RENAME")
    
    # Tampilkan daftar folder
    print(f"\n📋 Folder yang akan diproses:")
    total_files = 0
    for folder in subfolders:
        file_count = len([f for f in folder.iterdir() if f.is_file()])
        total_files += file_count
        print(f"  📁 {folder.name} ({file_count} files) → '{folder.name} 1', '{folder.name} 2', dst.")
    
    print(f"\n📊 Total file keseluruhan: {total_files}")
    
    if not preview:
        # Konfirmasi
        confirm = input(f"\n❓ Lanjutkan auto rename? (y/n): ").strip().lower()
        if confirm not in ['y', 'yes', 'ya']:
            print("❌ Auto rename dibatalkan.")
            return
    
    print(f"\n{'🔍 Memulai preview...' if preview else '🔄 Memulai auto rename...'}")
    print("=" * 60)
    
    total_success = 0
    total_failed = 0
    
    for folder_idx, folder in enumerate(subfolders, 1):
        print(f"\n📁 [{folder_idx}/{len(subfolders)}] {folder.name}")
        
        success, failed = rename_folder_by_name(folder, start_number, preview)
        total_success += success
        total_failed += failed
        
        # Jeda kecil
        if not preview:
            time.sleep(0.1)
    
    # Summary akhir
    print("\n" + "=" * 60)
    if preview:
        print(f"🔍 PREVIEW SELESAI!")
        print(f"   📊 Total file yang akan direname: {total_success}")
    else:
        print(f"🎉 AUTO RENAME SELESAI!")
        print(f"   ✅ Total file berhasil: {total_success}")
        print(f"   ❌ Total file gagal: {total_failed}")
    print(f"   📁 Total folder diproses: {len(subfolders)}")

def main():
    print("🔄 AUTO RENAME BY FOLDER NAME")
    print("=" * 50)
    print("Script ini akan rename file menggunakan nama folder masing-masing")
    print("Contoh: Folder 'Anisolabididae' → 'Anisolabididae 1.jpg', 'Anisolabididae 2.png', dst.")
    print()
    
    # Contoh direktori yang umum
    common_dirs = [
        "ResizedDatasetUnknown",
        "ResizedDataset", 
        "Dataset",
        "raw_data"
    ]
    
    print("Pilih directory:")
    for i, dir_path in enumerate(common_dirs, 1):
        if Path(dir_path).exists():
            subfolder_count = len([f for f in Path(dir_path).iterdir() if f.is_dir()])
            print(f"  {i}. {dir_path} ({subfolder_count} folders)")
        else:
            print(f"  {i}. {dir_path} (tidak ditemukan)")
    
    print(f"  {len(common_dirs)+1}. Input manual")
    print(f"  {len(common_dirs)+2}. Single folder")
    
    try:
        choice = int(input(f"\nPilih (1-{len(common_dirs)+2}): "))
        
        if 1 <= choice <= len(common_dirs):
            selected_dir = common_dirs[choice-1]
            
            start_num = input("🔢 Nomor awal (default: 1): ").strip()
            start_num = int(start_num) if start_num else 1
            
            # Preview dulu
            auto_rename_multiple_folders(selected_dir, start_num, preview=True)
            
            # Konfirmasi untuk rename
            print("\n" + "="*50)
            confirm = input("❓ Lanjutkan dengan rename sesungguhnya? (y/n): ").strip().lower()
            if confirm in ['y', 'yes', 'ya']:
                auto_rename_multiple_folders(selected_dir, start_num, preview=False)
            else:
                print("❌ Rename dibatalkan.")
            
        elif choice == len(common_dirs)+1:
            dir_path = input("📁 Masukkan path directory: ").strip().strip('"')
            start_num = input("🔢 Nomor awal (default: 1): ").strip()
            start_num = int(start_num) if start_num else 1
            
            # Preview dulu
            auto_rename_multiple_folders(dir_path, start_num, preview=True)
            
            # Konfirmasi untuk rename
            print("\n" + "="*50)
            confirm = input("❓ Lanjutkan dengan rename sesungguhnya? (y/n): ").strip().lower()
            if confirm in ['y', 'yes', 'ya']:
                auto_rename_multiple_folders(dir_path, start_num, preview=False)
            else:
                print("❌ Rename dibatalkan.")
                
        elif choice == len(common_dirs)+2:
            folder_path = input("📁 Masukkan path folder: ").strip().strip('"')
            start_num = input("🔢 Nomor awal (default: 1): ").strip()
            start_num = int(start_num) if start_num else 1
            
            # Preview dulu
            print("\n" + "="*50)
            rename_folder_by_name(folder_path, start_num, preview=True)
            
            # Konfirmasi untuk rename
            print("\n" + "="*50)
            confirm = input("❓ Lanjutkan dengan rename sesungguhnya? (y/n): ").strip().lower()
            if confirm in ['y', 'yes', 'ya']:
                rename_folder_by_name(folder_path, start_num, preview=False)
            else:
                print("❌ Rename dibatalkan.")
        else:
            print("❌ Pilihan tidak valid!")
            
    except (ValueError, KeyboardInterrupt):
        print("\n❌ Program dibatalkan.")

if __name__ == "__main__":
    main()
