# Bird Classification with Custom CNN

## 🐦 Overview

Proyek ini mengimplementasikan klasifikasi burung menggunakan arsitektur CNN custom yang terinspirasi dari notebook klasifikasi tanaman melon, tetapi disesuaikan khusus untuk dataset burung. Sistem ini dapat mengklasifikasikan 4 spesies burung dengan akurasi tinggi.

## 📁 File Structure

```
├── bird_classification_cnn_custom.ipynb    # Notebook utama untuk training
├── bird_classification_cnn_evaluation.py   # Script evaluasi dan deployment
├── README_BIRD_CNN_CUSTOM.md              # Dokumentasi ini
├── models/                                # Direktori untuk menyimpan model
│   ├── best_bird_cnn_model.h5            # Model terbaik (H5 format)
│   ├── bird_classification_cnn.tflite    # Model TensorFlow Lite
│   └── model_metadata.json               # Metadata model
├── results/                               # Hasil evaluasi
│   ├── training_history.csv              # History training
│   ├── confusion_matrix.png              # Confusion matrix
│   ├── classification_report.txt         # Classification report
│   └── sample_predictions.png            # Visualisasi prediksi sample
└── preview_augmentation/                  # Preview augmentasi data
```

## 🎯 Dataset yang Didukung

Sistem ini dirancang untuk bekerja dengan dataset burung Anda yang memiliki struktur:

```
Dataset/
├── Lonchura leucogastroides/    # Javan Munia
├── Lonchura maja/               # White-headed Munia  
├── Lonchura punctulata/         # Scaly-breasted Munia
└── Passer montanus/             # Eurasian Tree Sparrow
```

Atau:

```
ResizedDataset/                  # Dataset yang sudah diproses ke 224x224
├── Lonchura leucogastroides/
├── Lonchura maja/
├── Lonchura punctulata/
└── Passer montanus/
```

## 🏗️ Arsitektur Model

### Custom CNN Architecture:
- **Input**: 224×224×3 (RGB images)
- **Block 1**: Conv2D(32) → Conv2D(32) → MaxPool → Dropout(0.25)
- **Block 2**: Conv2D(64) → Conv2D(64) → MaxPool → Dropout(0.25)
- **Block 3**: Conv2D(128) → Conv2D(128) → MaxPool → Dropout(0.25)
- **Block 4**: Conv2D(256) → Conv2D(256) → MaxPool → Dropout(0.25)
- **Classifier**: Flatten → Dense(512) → Dropout(0.5) → Dense(256) → Dropout(0.5) → Dense(4, softmax)

### Key Features:
- ✅ **Double Conv Layers**: Setiap block memiliki 2 conv layers untuk ekstraksi fitur yang lebih baik
- ✅ **Progressive Filters**: Filter meningkat dari 32 → 64 → 128 → 256
- ✅ **Dropout Regularization**: Mencegah overfitting dengan dropout 0.25-0.5
- ✅ **Batch Normalization**: Implicit melalui padding='same'
- ✅ **Adaptive Learning Rate**: Learning rate scheduling otomatis

## 🚀 Cara Penggunaan

### 1. Training Model (Jupyter Notebook)

```bash
# Buka notebook
jupyter notebook bird_classification_cnn_custom.ipynb

# Atau gunakan Jupyter Lab
jupyter lab bird_classification_cnn_custom.ipynb
```

**Langkah-langkah dalam notebook:**

1. **Setup Environment**: Import libraries dan konfigurasi
2. **Dataset Detection**: Otomatis mendeteksi dataset yang tersedia
3. **Data Augmentation**: Preview dan setup augmentasi data
4. **Model Architecture**: Definisi arsitektur CNN custom
5. **Training Configuration**: Setup callbacks dan training parameters
6. **Model Training**: Training dengan monitoring real-time
7. **Visualization**: Training history dan feature maps
8. **Evaluation**: Evaluasi performa model

### 2. Evaluasi dan Deployment (Python Script)

```bash
# Jalankan evaluasi lengkap
python bird_classification_cnn_evaluation.py
```

**Script akan melakukan:**
- ✅ Load model terbaik
- ✅ Evaluasi pada validation set
- ✅ Generate confusion matrix
- ✅ Visualisasi sample predictions
- ✅ Convert ke TensorFlow Lite
- ✅ Buat metadata model

### 3. Manual Evaluation

```python
from bird_classification_cnn_evaluation import *

# Evaluasi model
results = evaluate_model(
    model_path='./models/best_bird_cnn_model.h5',
    data_dir='./ResizedDataset',
    results_dir='./results'
)

# Visualisasi prediksi
visualize_sample_predictions(
    model_path='./models/best_bird_cnn_model.h5',
    data_dir='./ResizedDataset',
    n_samples=12
)

# Convert ke TensorFlow Lite
tflite_path, h5_size, tflite_size = convert_to_tflite(
    model_path='./models/best_bird_cnn_model.h5'
)
```

## ⚙️ Konfigurasi Training

### Data Augmentation:
```python
train_datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=15,        # Rotasi 15° (disesuaikan untuk burung)
    width_shift_range=0.1,    # Shift horizontal 10%
    height_shift_range=0.1,   # Shift vertikal 10%
    shear_range=0.1,          # Shear 10%
    zoom_range=0.15,          # Zoom 15%
    horizontal_flip=True,     # Flip horizontal
    vertical_flip=False,      # Tidak flip vertikal (burung)
    validation_split=0.2      # 20% untuk validasi
)
```

### Training Parameters:
- **Batch Size**: 16 (optimal untuk CNN custom)
- **Learning Rate**: 0.001 (Adam optimizer)
- **Max Epochs**: 50 (dengan early stopping)
- **Validation Split**: 20%
- **Early Stopping**: Patience 15 epochs
- **Model Checkpoint**: Save best model berdasarkan val_accuracy

### Callbacks:
- ✅ **ModelCheckpoint**: Simpan model terbaik
- ✅ **EarlyStopping**: Stop jika tidak ada improvement
- ✅ **AccuracyThreshold**: Stop pada akurasi 92%
- ✅ **LearningRateScheduler**: Adaptive learning rate

## 📊 Expected Performance

### Typical Results:
- **Training Accuracy**: 85-95%
- **Validation Accuracy**: 80-90%
- **Model Size**: ~15-25 MB (H5)
- **TFLite Size**: ~8-15 MB
- **Training Time**: 30-60 menit (tergantung hardware)

### Performance Factors:
- 🎯 **Dataset Quality**: Gambar berkualitas tinggi = akurasi lebih baik
- 📊 **Dataset Balance**: Distribusi kelas yang seimbang penting
- 🔧 **Hyperparameters**: Learning rate dan batch size optimal
- 💻 **Hardware**: GPU mempercepat training significantly

## 🔧 Troubleshooting

### Common Issues:

1. **Dataset Not Found**
   ```
   ❌ TIDAK DITEMUKAN DATASET YANG VALID!
   ```
   **Solution**: Pastikan struktur direktori sesuai dan ada gambar di setiap kelas

2. **Memory Error**
   ```
   ResourceExhaustedError: OOM when allocating tensor
   ```
   **Solution**: Kurangi batch_size dari 16 ke 8 atau 4

3. **Low Accuracy**
   ```
   Validation accuracy stuck at ~25%
   ```
   **Solution**: 
   - Periksa kualitas dataset
   - Tambah data augmentation
   - Adjust learning rate

4. **Model Not Saving**
   ```
   Permission denied: ./models/
   ```
   **Solution**: Buat direktori manual atau check permissions

### Performance Optimization:

1. **Untuk Dataset Kecil** (<1000 images):
   - Kurangi dropout ke 0.2
   - Tambah data augmentation
   - Gunakan transfer learning

2. **Untuk Dataset Besar** (>5000 images):
   - Tingkatkan batch size ke 32
   - Kurangi data augmentation
   - Tambah regularization

3. **Untuk Training Cepat**:
   - Gunakan GPU
   - Tingkatkan batch size
   - Kurangi max epochs

## 📱 Mobile Deployment

Model yang dihasilkan sudah siap untuk deployment mobile:

### TensorFlow Lite:
```python
# Load TFLite model
interpreter = tf.lite.Interpreter(model_path='bird_classification_cnn.tflite')
interpreter.allocate_tensors()

# Get input/output details
input_details = interpreter.get_input_details()
output_details = interpreter.get_output_details()

# Inference
interpreter.set_tensor(input_details[0]['index'], input_data)
interpreter.invoke()
output_data = interpreter.get_tensor(output_details[0]['index'])
```

### Android Integration:
- Model size: ~8-15 MB (cocok untuk mobile)
- Input: 224×224×3 RGB image
- Output: 4 class probabilities
- Preprocessing: Normalize ke [0,1]

## 🆚 Perbandingan dengan MobileNetV2

| Aspect | Custom CNN | MobileNetV2 Transfer Learning |
|--------|------------|------------------------------|
| **Model Size** | 15-25 MB | 10-15 MB |
| **Training Time** | 30-60 min | 15-30 min |
| **Accuracy** | 80-90% | 85-95% |
| **Customization** | High | Medium |
| **Mobile Ready** | ✅ | ✅ |
| **Feature Control** | Full | Limited |

## 📞 Support

Jika mengalami masalah:

1. **Check Dependencies**: Pastikan semua library terinstall
2. **Verify Dataset**: Struktur direktori dan format gambar
3. **Monitor Resources**: RAM dan GPU usage
4. **Check Logs**: Error messages untuk debugging

## 🎉 Kesimpulan

Notebook dan script ini menyediakan implementasi lengkap untuk klasifikasi burung menggunakan CNN custom dengan:

- ✅ **Deteksi dataset otomatis**
- ✅ **Augmentasi data yang disesuaikan**
- ✅ **Arsitektur CNN yang optimal**
- ✅ **Training dengan monitoring lengkap**
- ✅ **Evaluasi komprehensif**
- ✅ **Deployment ready (TFLite)**
- ✅ **Visualisasi yang informatif**

Sistem ini menggabungkan pendekatan dari notebook melon plant dengan optimisasi khusus untuk klasifikasi burung, menghasilkan solusi yang robust dan siap production.
