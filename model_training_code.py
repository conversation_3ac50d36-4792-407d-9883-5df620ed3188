# ===== KODE MODEL DAN TRAINING - COPY PASTE KE CELL NOTEBOOK SETELAH DATA SETUP =====

# ===== MEMBUAT MODEL MOBILENETV2 =====

print("🏗️  Membuat MobileNetV2 model...")

# Load MobileNetV2 sebagai base model
base_model = MobileNetV2(weights='imagenet', include_top=False, input_shape=input_shape)
print("✅ MobileNetV2 base model loaded")

# Freeze layers di base model
for layer in base_model.layers:
    layer.trainable = False
print("✅ Base model layers frozen")

# Tambahkan custom head
x = base_model.output
x = GlobalAveragePooling2D()(x)
x = Dropout(0.5)(x)
x = Dense(1024, activation='relu')(x)
x = Dropout(0.5)(x)
predictions = Dense(num_classes, activation='softmax')(x)

# Gabungkan base model dan custom head
model = Model(inputs=base_model.input, outputs=predictions)
print("✅ Custom head added")

# Fine-tune model dengan unfreeze beberapa layer terakhir
for layer in base_model.layers[:-10]:
    layer.trainable = False

# Implement learning rate scheduling
lr_schedule = tf.keras.optimizers.schedules.ExponentialDecay(
    initial_learning_rate=1e-4,
    decay_steps=1000,
    decay_rate=0.9
)
optimizer = Adam(learning_rate=lr_schedule)

# Compile model
model.compile(optimizer=optimizer, loss='categorical_crossentropy', metrics=['accuracy'])

print("✅ Model compiled successfully!")
print(f"📊 Total parameters: {model.count_params():,}")

# Tampilkan summary model
print("\n📋 Model Summary:")
model.summary()

# ===== TRAINING MODEL =====

print(f"\n🚀 Memulai training...")

# Implement early stopping callback
early_stopping = tf.keras.callbacks.EarlyStopping(
    monitor='val_loss',
    patience=5,
    restore_best_weights=True,
    verbose=1
)

# Model checkpoint callback
checkpoint = tf.keras.callbacks.ModelCheckpoint(
    'best_birds_model.h5',
    monitor='val_accuracy',
    save_best_only=True,
    verbose=1
)

# Reduce learning rate callback
reduce_lr = tf.keras.callbacks.ReduceLROnPlateau(
    monitor='val_loss',
    factor=0.2,
    patience=3,
    min_lr=1e-7,
    verbose=1
)

# Training dengan callbacks
print("🎯 Training configuration:")
print(f"  - Epochs: 30")
print(f"  - Early stopping patience: 5")
print(f"  - Learning rate reduction: enabled")
print(f"  - Model checkpoint: enabled")

history = model.fit(
    train_generator,
    epochs=30,
    validation_data=valid_generator,
    callbacks=[early_stopping, checkpoint, reduce_lr],
    verbose=1
)

print("✅ Training selesai!")

# ===== EVALUASI MODEL =====

print(f"\n📊 Evaluating model...")

# Evaluasi pada validation data
test_loss, test_accuracy = model.evaluate(valid_generator, verbose=1)
print(f"\n📈 Final Results:")
print(f"Test Loss: {test_loss:.4f}")
print(f"Test Accuracy: {test_accuracy:.4f}")

# ===== VISUALISASI HASIL TRAINING =====

print(f"\n📊 Plotting training history...")

# Plot training history
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))

# Plot accuracy
ax1.plot(history.history['accuracy'], label='Training Accuracy', linewidth=2)
ax1.plot(history.history['val_accuracy'], label='Validation Accuracy', linewidth=2)
ax1.set_title('Model Accuracy', fontsize=14, fontweight='bold')
ax1.set_xlabel('Epoch')
ax1.set_ylabel('Accuracy')
ax1.legend()
ax1.grid(True, alpha=0.3)

# Plot loss
ax2.plot(history.history['loss'], label='Training Loss', linewidth=2)
ax2.plot(history.history['val_loss'], label='Validation Loss', linewidth=2)
ax2.set_title('Model Loss', fontsize=14, fontweight='bold')
ax2.set_xlabel('Epoch')
ax2.set_ylabel('Loss')
ax2.legend()
ax2.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig('training_history.png', dpi=300, bbox_inches='tight')
plt.show()

print("✅ Training history plot saved as 'training_history.png'")

# ===== CONFUSION MATRIX =====

print(f"\n📊 Generating confusion matrix...")

# Prediksi pada validation data
valid_generator.reset()  # Reset generator
predictions = model.predict(valid_generator, verbose=1)
predicted_classes = np.argmax(predictions, axis=1)

# True labels
true_classes = valid_generator.classes
class_labels = list(valid_generator.class_indices.keys())

# Confusion matrix
cm = confusion_matrix(true_classes, predicted_classes)

# Plot confusion matrix
plt.figure(figsize=(10, 8))
sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
            xticklabels=class_labels, yticklabels=class_labels)
plt.title('Confusion Matrix', fontsize=16, fontweight='bold')
plt.xlabel('Predicted Label', fontsize=12)
plt.ylabel('True Label', fontsize=12)
plt.xticks(rotation=45, ha='right')
plt.yticks(rotation=0)
plt.tight_layout()
plt.savefig('confusion_matrix.png', dpi=300, bbox_inches='tight')
plt.show()

print("✅ Confusion matrix saved as 'confusion_matrix.png'")

# ===== SIMPAN MODEL =====

# Simpan model final
model.save('birds_classification_local.h5')
print(f"\n✅ Model saved as 'birds_classification_local.h5'")

# Simpan model dalam format SavedModel (untuk deployment)
model.save('birds_model_savedmodel', save_format='tf')
print(f"✅ Model saved as 'birds_model_savedmodel' (SavedModel format)")

# ===== RINGKASAN HASIL =====

print(f"\n" + "="*60)
print(f"🎉 TRAINING COMPLETED SUCCESSFULLY!")
print(f"="*60)
print(f"📊 Final Results:")
print(f"  - Test Accuracy: {test_accuracy:.4f} ({test_accuracy*100:.2f}%)")
print(f"  - Test Loss: {test_loss:.4f}")
print(f"  - Total Epochs: {len(history.history['loss'])}")
print(f"  - Training Samples: {train_generator.samples}")
print(f"  - Validation Samples: {valid_generator.samples}")
print(f"\n📁 Files Generated:")
print(f"  - best_birds_model.h5 (best model during training)")
print(f"  - birds_classification_local.h5 (final model)")
print(f"  - birds_model_savedmodel/ (SavedModel format)")
print(f"  - training_history.png (training plots)")
print(f"  - confusion_matrix.png (confusion matrix)")
print(f"\n🚀 Model siap digunakan untuk prediksi!")

# ===== FUNGSI PREDIKSI SEDERHANA =====

def predict_bird_image(image_path, model=model, class_labels=class_labels):
    """
    Fungsi untuk memprediksi gambar burung
    
    Args:
        image_path: path ke gambar
        model: model yang sudah ditraining
        class_labels: list nama kelas
    
    Returns:
        prediction: nama kelas yang diprediksi
        confidence: confidence score
    """
    # Load dan preprocess gambar
    img = tf.keras.preprocessing.image.load_img(image_path, target_size=(224, 224))
    img_array = tf.keras.preprocessing.image.img_to_array(img)
    img_array = np.expand_dims(img_array, axis=0)
    img_array = img_array / 255.0
    
    # Prediksi
    predictions = model.predict(img_array)
    predicted_class_idx = np.argmax(predictions[0])
    confidence = predictions[0][predicted_class_idx]
    
    predicted_class = class_labels[predicted_class_idx]
    
    return predicted_class, confidence

print(f"\n✅ Fungsi prediksi 'predict_bird_image()' siap digunakan!")
print(f"📋 Contoh penggunaan:")
print(f"   prediction, confidence = predict_bird_image('path/to/image.jpg')")
print(f"   print(f'Predicted: {{prediction}} ({{confidence:.2f}})')")
