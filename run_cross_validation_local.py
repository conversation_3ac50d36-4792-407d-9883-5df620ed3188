# -*- coding: utf-8 -*-
"""
Cross Validation untuk Klasifikasi Burung dengan MobileNetV2
Disesuaikan untuk dataset lokal dengan struktur SplitDataset
"""

import tensorflow as tf
import os
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from tensorflow.keras.layers import GlobalAveragePooling2D, Dropout, Dense
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.applications import MobileNetV2
from tensorflow.keras.models import Model
from tensorflow.keras.preprocessing.image import ImageDataGenerator, load_img, img_to_array
from sklearn.model_selection import StratifiedKFold
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from PIL import Image
import warnings
warnings.filterwarnings('ignore')

print("=== CROSS VALIDATION KLASIFIKASI BURUNG DENGAN MOBILENETV2 ===")
print("Versi untuk dataset lokal")

# ===== KONFIGURASI =====
INPUT_SHAPE = (224, 224, 3)
NUM_CLASSES = 5
BATCH_SIZE = 32
N_SPLITS = 5
EPOCHS = 20
LEARNING_RATE = 0.0001

# Path dataset
DATASET_DIR = "SplitDataset"
TRAIN_DIR = os.path.join(DATASET_DIR, "train")
VALID_DIR = os.path.join(DATASET_DIR, "validation")

print(f"Input shape: {INPUT_SHAPE}")
print(f"Jumlah kelas: {NUM_CLASSES}")
print(f"Batch size: {BATCH_SIZE}")
print(f"Jumlah fold: {N_SPLITS}")
print(f"Epochs per fold: {EPOCHS}")

# ===== FUNGSI HELPER =====
def load_and_preprocess_image(image_path):
    """Load dan preprocess gambar individual"""
    try:
        image = load_img(image_path, target_size=(224, 224))
        image_array = img_to_array(image)
        image_array = image_array / 255.0  # Normalisasi
        return image_array
    except Exception as e:
        print(f"Error loading image {image_path}: {e}")
        return None

def load_data_for_cv():
    """Load semua data untuk cross validation"""
    print("\n=== LOADING DATA UNTUK CROSS VALIDATION ===")
    
    all_image_paths = []
    all_labels = []
    class_names = []
    
    # Gabungkan data dari train dan validation
    for data_dir in [TRAIN_DIR, VALID_DIR]:
        if not os.path.exists(data_dir):
            print(f"ERROR: Directory {data_dir} tidak ditemukan!")
            return None, None, None
            
        classes = sorted([d for d in os.listdir(data_dir) if os.path.isdir(os.path.join(data_dir, d))])
        
        if not class_names:
            class_names = classes
            print(f"Kelas yang ditemukan: {class_names}")
        
        for class_idx, class_name in enumerate(classes):
            class_dir = os.path.join(data_dir, class_name)
            image_files = [f for f in os.listdir(class_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
            
            for image_file in image_files:
                image_path = os.path.join(class_dir, image_file)
                all_image_paths.append(image_path)
                all_labels.append(class_idx)
    
    print(f"Total gambar: {len(all_image_paths)}")
    print(f"Distribusi kelas: {np.bincount(all_labels)}")
    
    return np.array(all_image_paths), np.array(all_labels), class_names

def create_mobilenetv2_model():
    """Buat model MobileNetV2 dengan transfer learning"""
    # Load MobileNetV2 pre-trained
    base_model = MobileNetV2(
        weights='imagenet',
        include_top=False,
        input_shape=INPUT_SHAPE
    )
    
    # Freeze base model
    base_model.trainable = False
    
    # Tambahkan custom head
    inputs = tf.keras.Input(shape=INPUT_SHAPE)
    x = base_model(inputs, training=False)
    x = GlobalAveragePooling2D()(x)
    x = Dropout(0.2)(x)
    outputs = Dense(NUM_CLASSES, activation='softmax')(x)
    
    model = Model(inputs, outputs)
    
    # Compile model
    model.compile(
        optimizer=Adam(learning_rate=LEARNING_RATE),
        loss='sparse_categorical_crossentropy',
        metrics=['accuracy']
    )
    
    return model

# ===== LOAD DATA =====
image_paths, labels, class_names = load_data_for_cv()

if image_paths is None:
    print("ERROR: Gagal memuat data!")
    exit(1)

print(f"Kelas: {class_names}")

# ===== CROSS VALIDATION =====
print(f"\n=== MEMULAI {N_SPLITS}-FOLD CROSS VALIDATION ===")

# Setup StratifiedKFold
skf = StratifiedKFold(n_splits=N_SPLITS, shuffle=True, random_state=42)

# Variabel untuk menyimpan hasil
cv_scores = []
fold_histories = []
all_predictions = []
all_true_labels = []

# Data augmentation
datagen = ImageDataGenerator(
    rotation_range=20,
    width_shift_range=0.2,
    height_shift_range=0.2,
    shear_range=0.2,
    zoom_range=0.2,
    horizontal_flip=True,
    fill_mode='nearest'
)

# Loop untuk setiap fold
for fold, (train_idx, val_idx) in enumerate(skf.split(image_paths, labels)):
    print(f"\n--- FOLD {fold + 1}/{N_SPLITS} ---")
    
    # Split data untuk fold ini
    train_paths = image_paths[train_idx]
    train_labels = labels[train_idx]
    val_paths = image_paths[val_idx]
    val_labels = labels[val_idx]
    
    print(f"Training samples: {len(train_paths)}")
    print(f"Validation samples: {len(val_paths)}")
    
    # Load dan preprocess training data
    print("Loading training data...")
    X_train = []
    y_train = []
    
    for i, (path, label) in enumerate(zip(train_paths, train_labels)):
        if i % 100 == 0:
            print(f"  Processed {i}/{len(train_paths)} training images")
        
        img = load_and_preprocess_image(path)
        if img is not None:
            X_train.append(img)
            y_train.append(label)
    
    X_train = np.array(X_train)
    y_train = np.array(y_train)
    
    # Load dan preprocess validation data
    print("Loading validation data...")
    X_val = []
    y_val = []
    
    for i, (path, label) in enumerate(zip(val_paths, val_labels)):
        if i % 50 == 0:
            print(f"  Processed {i}/{len(val_paths)} validation images")
        
        img = load_and_preprocess_image(path)
        if img is not None:
            X_val.append(img)
            y_val.append(label)
    
    X_val = np.array(X_val)
    y_val = np.array(y_val)
    
    print(f"Training data shape: {X_train.shape}")
    print(f"Validation data shape: {X_val.shape}")
    
    # Buat model untuk fold ini
    model = create_mobilenetv2_model()
    
    # Training dengan data augmentation
    print("Training model...")
    history = model.fit(
        datagen.flow(X_train, y_train, batch_size=BATCH_SIZE),
        steps_per_epoch=len(X_train) // BATCH_SIZE,
        epochs=EPOCHS,
        validation_data=(X_val, y_val),
        verbose=1
    )
    
    # Evaluasi
    val_predictions = model.predict(X_val)
    val_pred_classes = np.argmax(val_predictions, axis=1)
    
    # Hitung akurasi fold ini
    fold_accuracy = accuracy_score(y_val, val_pred_classes)
    cv_scores.append(fold_accuracy)
    fold_histories.append(history)
    
    # Simpan prediksi untuk confusion matrix keseluruhan
    all_predictions.extend(val_pred_classes)
    all_true_labels.extend(y_val)
    
    print(f"Fold {fold + 1} Accuracy: {fold_accuracy:.4f}")
    
    # Bersihkan memory
    del model, X_train, y_train, X_val, y_val
    tf.keras.backend.clear_session()

# ===== HASIL CROSS VALIDATION =====
print(f"\n=== HASIL CROSS VALIDATION ===")
print(f"Akurasi per fold: {[f'{score:.4f}' for score in cv_scores]}")
print(f"Mean CV Accuracy: {np.mean(cv_scores):.4f} ± {np.std(cv_scores):.4f}")

# Confusion Matrix
print(f"\n=== CONFUSION MATRIX ===")
cm = confusion_matrix(all_true_labels, all_predictions)
print("Confusion Matrix:")
print(cm)

# Classification Report
print(f"\n=== CLASSIFICATION REPORT ===")
report = classification_report(all_true_labels, all_predictions, target_names=class_names)
print(report)

print(f"\n=== CROSS VALIDATION SELESAI ===")
print(f"Mean Accuracy: {np.mean(cv_scores):.4f}")
print(f"Standard Deviation: {np.std(cv_scores):.4f}")
