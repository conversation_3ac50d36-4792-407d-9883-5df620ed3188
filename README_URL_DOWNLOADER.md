# URL Downloader Bot

Bot Python untuk mendownload file dari URLs yang ada dalam file text. Mendukung format CSV dan text biasa dengan fitur download paralel, retry otomatis, dan progress tracking.

## Fitur

- ✅ **Multi-format support**: CSV (tab/comma separated) dan plain text
- ✅ **Parallel downloads**: Download multiple files secara bersamaan
- ✅ **Auto retry**: Retry otomatis jika download gagal
- ✅ **Progress tracking**: Progress bar dengan informasi detail
- ✅ **Smart filename**: Generate nama file otomatis dari URL atau metadata
- ✅ **Resume capability**: Skip file yang sudah ada
- ✅ **Comprehensive logging**: Log detail ke file dan console
- ✅ **Error handling**: Robust error handling dan recovery
- ✅ **Rate limiting**: Delay antar download untuk menghormati server

## Instalasi

1. Install dependencies:
```bash
pip install -r requirements.txt
```

Atau install manual:
```bash
pip install requests tqdm
```

## Penggunaan

### 1. Quick Start - Download multimedia.txt

Untuk mendownload semua file dari multimedia.txt:

```bash
python download_multimedia.py
```

Untuk demo dengan 10 file pertama saja:

```bash
python demo_download.py
```

### 2. Menggunakan Script Utama (Recommended)

```bash
python run_downloader.py <input_file> [options]
```

#### Contoh Penggunaan:

```bash
# Download semua file dari multimedia.txt
python run_downloader.py multimedia.txt

# Download ke folder custom
python run_downloader.py multimedia.txt -o my_images

# Download 50 file pertama saja (untuk testing)
python run_downloader.py multimedia.txt --limit 50

# Download dengan 2 workers (lebih aman untuk server)
python run_downloader.py multimedia.txt -w 2

# Sequential download (paling aman)
python run_downloader.py multimedia.txt --sequential

# Untuk koneksi lambat
python run_downloader.py multimedia.txt -w 2 -t 60 -d 2.0
```

#### Parameter:

- `input_file`: File yang berisi URLs (wajib)
- `-o, --output`: Directory output (default: downloads)
- `-w, --workers`: Jumlah parallel workers (default: 3)
- `-t, --timeout`: Timeout per request dalam detik (default: 60)
- `-r, --retry`: Jumlah retry attempts (default: 3)
- `-d, --delay`: Delay antar download dalam detik (default: 1.0)
- `--sequential`: Gunakan download sequential
- `--limit`: Batasi jumlah file yang didownload

### 3. Menggunakan Bot Langsung (Advanced)

```bash
python url_downloader_bot.py <input_file> [options]
```

Parameter sama seperti di atas, tapi dengan default yang berbeda.

## Format File Input

### 1. CSV Format (seperti multimedia.txt)

Bot akan otomatis mendeteksi format CSV dan mencari URLs di:
- Kolom `identifier` (prioritas utama)
- Kolom manapun yang mengandung `http`

```csv
gbifID	type	format	identifier	title
123	StillImage	image/jpeg	https://example.com/image1.jpg	Image 1
124	StillImage	image/jpeg	https://example.com/image2.jpg	Image 2
```

### 2. Plain Text Format

Satu URL per baris atau URLs dalam teks:

```
https://example.com/file1.jpg
https://example.com/file2.png
Some text with https://example.com/file3.gif embedded URL
```

## Output

### File Structure
```
multimedia_downloads/
├── gbif_5237921317.jpeg    (847 KB)
├── gbif_5205900201.jpg     (33 KB)
├── gbif_5168542083.jpg     (335 KB)
├── gbif_5166938255.jpg     (381 KB)
└── ...
```

### Logs
- Console output dengan progress bar real-time
- File log: `downloader.log`
- Progress tracking dengan tqdm

### Summary Report
```
==================================================
DOWNLOAD SUMMARY
==================================================
Total URLs: 261
Successfully downloaded: 250
Failed: 5
Skipped (already exists): 6
Success rate: 95.8%
Files saved to: /path/to/multimedia_downloads
==================================================
```

### Test Results

Bot telah ditest dengan file multimedia.txt dan berhasil:
- ✅ Mengidentifikasi 261 URL gambar valid dari 265 baris data
- ✅ Mendownload file dengan nama `gbif_[ID].[ext]`
- ✅ Skip file yang sudah ada (resume capability)
- ✅ Parallel download dengan progress bar
- ✅ Error handling dan retry otomatis

## Fitur Advanced

### 1. Smart Filename Generation

Bot akan generate nama file berdasarkan:
1. `gbifID` jika tersedia (untuk CSV)
2. Nama file dari URL
3. Nomor urut jika tidak ada nama yang jelas

### 2. File Type Detection

Bot akan mendeteksi ekstensi file dari:
1. URL path
2. Content-Type header
3. Default ke `.jpg` untuk gambar

### 3. Resume Downloads

Jika download terputus, jalankan ulang script. Bot akan skip file yang sudah ada dan melanjutkan yang belum.

### 4. Error Handling

- Network timeout: Retry dengan exponential backoff
- HTTP errors: Log error dan lanjut ke file berikutnya
- File system errors: Log dan skip
- Invalid URLs: Skip dengan warning

## Troubleshooting

### 1. Download Lambat
```bash
# Kurangi jumlah workers
python url_downloader_bot.py urls.txt -w 2

# Tambah delay
python url_downloader_bot.py urls.txt -d 2.0
```

### 2. Banyak Timeout
```bash
# Increase timeout
python url_downloader_bot.py urls.txt -t 60
```

### 3. Server Blocking
```bash
# Use sequential download dengan delay
python url_downloader_bot.py urls.txt --sequential -d 3.0
```

### 4. Memory Issues
```bash
# Reduce workers
python url_downloader_bot.py urls.txt -w 1
```

## Contoh Log Output

```
2025-01-07 10:30:15 - INFO - Found 267 URLs in multimedia.txt
2025-01-07 10:30:15 - INFO - Starting download of 267 files to multimedia_downloads
Downloading: 45%|████▌     | 120/267 [02:15<02:45, 1.12it/s]
2025-01-07 10:32:30 - INFO - Downloaded: gbif_5237921317.jpeg (245632 bytes)
2025-01-07 10:32:31 - WARNING - Attempt 1 failed for gbif_5205900201.jpg: Connection timeout
2025-01-07 10:32:33 - INFO - Downloaded: gbif_5205900201.jpg (189456 bytes)
```

## Tips Penggunaan

1. **Untuk server yang sensitif**: Gunakan delay yang lebih besar dan workers yang lebih sedikit
2. **Untuk file besar**: Increase timeout
3. **Untuk resume download**: Jalankan ulang script yang sama
4. **Untuk monitoring**: Check file `downloader.log` untuk detail error

## Lisensi

Script ini dibuat untuk keperluan penelitian dan pendidikan. Pastikan untuk menghormati terms of service dari server yang Anda akses.
