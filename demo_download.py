#!/usr/bin/env python3
"""
Demo script untuk mendownload 10 file pertama dari multimedia.txt
"""

import os
import sys
from url_downloader_bot import URLDownloaderBot

def main():
    # File input
    input_file = "multimedia.txt"
    
    # Check if file exists
    if not os.path.exists(input_file):
        print(f"Error: File '{input_file}' tidak ditemukan!")
        sys.exit(1)
    
    print("="*60)
    print("DEMO MULTIMEDIA DOWNLOADER BOT")
    print("="*60)
    print(f"Input file: {input_file}")
    print("Output directory: demo_downloads")
    print("Downloading first 10 files only...")
    print("="*60)
    
    # Create bot instance
    bot = URLDownloaderBot(
        input_file=input_file,
        output_dir="demo_downloads",
        max_workers=2,  # Lebih konservatif
        timeout=60,
        retry_count=3,
        delay_between_downloads=1.0
    )
    
    try:
        # Get URLs and limit to first 10
        urls = bot.extract_urls_from_file()[:10]
        print(f"Found {len(urls)} URLs to download:")
        
        for i, (url, filename) in enumerate(urls, 1):
            print(f"{i}. {filename}")
        
        print("\nStarting downloads...")
        
        # Download files
        bot.stats['total'] = len(urls)
        for i, (url, filename) in enumerate(urls, 1):
            print(f"\n[{i}/{len(urls)}] Downloading: {filename}")
            success = bot.download_file(url, filename)
            if not success:
                print(f"Failed to download: {filename}")
        
        bot.print_summary()
        
        print(f"\nDemo selesai! Check folder 'demo_downloads' untuk file yang telah didownload.")
        
    except KeyboardInterrupt:
        print("\n\nDownload dihentikan oleh user!")
        bot.print_summary()
    except Exception as e:
        print(f"Error tidak terduga: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
