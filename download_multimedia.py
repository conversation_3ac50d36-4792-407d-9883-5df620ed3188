#!/usr/bin/env python3
"""
Script untuk mendownload file multimedia dari multimedia.txt
"""

import os
import sys
from url_downloader_bot import URLDownloaderBot

def main():
    # File input
    input_file = "multimedia.txt"
    
    # Check if file exists
    if not os.path.exists(input_file):
        print(f"Error: File '{input_file}' tidak ditemukan!")
        print("Pastikan file multimedia.txt ada di directory yang sama.")
        sys.exit(1)
    
    print("="*60)
    print("MULTIMEDIA DOWNLOADER BOT")
    print("="*60)
    print(f"Input file: {input_file}")
    print("Output directory: multimedia_downloads")
    print("="*60)
    
    # Create bot instance dengan konfigurasi yang sesuai untuk multimedia
    bot = URLDownloaderBot(
        input_file=input_file,
        output_dir="multimedia_downloads",
        max_workers=3,  # Lebih konservatif untuk server iNaturalist
        timeout=60,     # Timeout lebih lama untuk file gambar
        retry_count=3,
        delay_between_downloads=1.0  # Delay lebih lama untuk menghormati server
    )
    
    try:
        # Start downloading
        bot.download_all(use_parallel=True)
        
        print("\nDownload selesai!")
        print(f"Check folder 'multimedia_downloads' untuk file yang telah didownload.")
        
    except KeyboardInterrupt:
        print("\n\nDownload dihentikan oleh user!")
        bot.print_summary()
    except Exception as e:
        print(f"Error tidak terduga: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
