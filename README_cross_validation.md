# Cross Validation dengan Pendekatan Procedural Programming

## Deskripsi
Repository ini berisi implementasi cross validation untuk klasifikasi burung menggunakan MobileNetV2 dengan dua pendekatan berbeda:

1. **Pendekatan Functional Programming** (file asli)
2. **Pendekatan Procedural Programming** (file baru)

## File-file yang Tersedia

### 1. `klasifikasi_burung_mobilenetv2.py`
- File asli dengan pendekatan functional programming
- Menggunakan function untuk modularitas
- Lebih terstruktur dan mudah di-maintain

### 2. `cross_validation_procedural.py`
- Implementasi lengkap dengan pendekatan procedural
- Tanpa menggunakan function
- Langkah-langkah eksplisit dan berurutan
- Cocok untuk pembelajaran step-by-step

### 3. `simple_cross_validation_procedural.py`
- Versi sederhana untuk pembelajaran
- Menggunakan data simulasi
- Lebih mudah dipahami untuk pemula
- Fokus pada konsep cross validation

## Perbedaan Utama

### Pendekatan Functional Programming
```python
def create_model():
    # Kode untuk membuat model
    return model

def load_data():
    # Kode untuk load data
    return data

def perform_cv():
    # Kode untuk cross validation
    return results

# Pemanggilan function
model = create_model()
data = load_data()
results = perform_cv()
```

### Pendekatan Procedural Programming
```python
# LANGKAH 1: Buat model
print("Membuat model...")
base_model = MobileNetV2(...)
# ... kode lengkap untuk membuat model

# LANGKAH 2: Load data
print("Loading data...")
# ... kode lengkap untuk load data

# LANGKAH 3: Cross validation
print("Memulai cross validation...")
for fold in range(n_folds):
    # ... kode lengkap untuk setiap fold
```

## Keunggulan Masing-masing Pendekatan

### Functional Programming
✅ **Keunggulan:**
- Kode lebih modular dan reusable
- Mudah di-debug dan di-test
- Lebih mudah di-maintain
- Menghindari repetisi kode

❌ **Kekurangan:**
- Mungkin lebih sulit dipahami untuk pemula
- Abstraksi bisa menyembunyikan detail

### Procedural Programming
✅ **Keunggulan:**
- Sangat mudah dipahami langkah demi langkah
- Cocok untuk pembelajaran
- Kontrol penuh atas setiap langkah
- Mudah dimodifikasi untuk eksperimen

❌ **Kekurangan:**
- Kode bisa menjadi panjang dan repetitif
- Lebih sulit di-maintain
- Rentan terhadap error karena repetisi

## Cara Menjalankan

### Untuk Data Asli (Google Colab)
```python
# Jalankan salah satu file berikut:
python cross_validation_procedural.py
```

### Untuk Pembelajaran (Data Simulasi)
```python
# Jalankan file sederhana:
python simple_cross_validation_procedural.py
```

## Struktur Cross Validation Procedural

### Langkah-langkah Utama:

1. **Setup Awal**
   - Definisi parameter
   - Import library

2. **Persiapan Data**
   - Mount Google Drive
   - Ekstrak dataset
   - Kumpulkan path gambar

3. **Mapping Label**
   - Buat mapping label ke indeks
   - Konversi label

4. **Inisialisasi CV**
   - Setup StratifiedKFold
   - Inisialisasi variabel hasil

5. **Loop Cross Validation**
   - Untuk setiap fold:
     - Bagi data train/validation
     - Load dan preprocess gambar
     - Buat model MobileNetV2
     - Training model
     - Evaluasi model
     - Simpan hasil

6. **Analisis Hasil**
   - Hitung statistik
   - Buat confusion matrix
   - Classification report

7. **Visualisasi**
   - Plot accuracy per fold
   - Plot training history
   - Confusion matrix heatmap

## Parameter yang Dapat Disesuaikan

```python
# Parameter utama
input_shape = (224, 224, 3)
num_classes = 5
batch_size = 128
n_splits = 5  # Jumlah fold

# Parameter training
epochs = 30
learning_rate = 1e-4
patience = 3  # Early stopping
```

## Output yang Dihasilkan

1. **Metrics per Fold**
   - Accuracy
   - Loss
   - Training history

2. **Overall Statistics**
   - Mean accuracy
   - Standard deviation
   - 95% confidence interval

3. **Visualisasi**
   - Confusion matrix
   - Accuracy plot per fold
   - Training/validation curves

4. **Classification Report**
   - Precision, recall, F1-score per kelas

## Tips Penggunaan

### Untuk Pemula:
1. Mulai dengan `simple_cross_validation_procedural.py`
2. Pahami setiap langkah dengan membaca komentar
3. Eksperimen dengan parameter yang berbeda

### Untuk Implementasi Nyata:
1. Gunakan `cross_validation_procedural.py`
2. Sesuaikan path dataset
3. Adjust parameter sesuai kebutuhan
4. Monitor penggunaan memory

## Troubleshooting

### Memory Issues:
- Kurangi batch_size
- Kurangi jumlah fold
- Tambahkan `tf.keras.backend.clear_session()`

### Training Lambat:
- Kurangi epochs
- Gunakan GPU jika tersedia
- Kurangi ukuran gambar

### Accuracy Rendah:
- Tambah epochs
- Sesuaikan learning rate
- Tambah data augmentation

## Kesimpulan

Pendekatan procedural programming sangat cocok untuk:
- **Pembelajaran** konsep cross validation
- **Eksperimen** dengan parameter berbeda
- **Debugging** langkah demi langkah
- **Pemahaman mendalam** tentang proses ML

Pilih pendekatan yang sesuai dengan tujuan dan level pemahaman Anda!
