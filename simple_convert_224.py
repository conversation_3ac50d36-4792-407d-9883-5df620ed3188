#!/usr/bin/env python3
"""
Script sederhana untuk mengkonversi gambar ke 224x224
Tinggal jalankan: python simple_convert_224.py
"""

import os
import cv2
import numpy as np
from PIL import Image

def convert_image_with_padding(input_path, output_path):
    """
    Konversi satu gambar ke 224x224 dengan padding hitam
    """
    try:
        # Baca gambar
        image = cv2.imread(input_path)
        if image is None:
            return False
        
        # Convert BGR ke RGB
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # Convert ke PIL
        pil_image = Image.fromarray(image)
        
        # Get ukuran asli
        width, height = pil_image.size
        
        # Hitung scale untuk fit ke 224x224
        scale = min(224 / width, 224 / height)
        
        # Ukuran baru setelah scale
        new_width = int(width * scale)
        new_height = int(height * scale)
        
        # Resize gambar
        resized = pil_image.resize((new_width, new_height), Image.LANCZOS)
        
        # Buat canvas 224x224 dengan background hitam
        result = Image.new('RGB', (224, 224), (0, 0, 0))
        
        # Hitung posisi untuk center gambar
        x = (224 - new_width) // 2
        y = (224 - new_height) // 2
        
        # Paste gambar ke center
        result.paste(resized, (x, y))
        
        # Save
        result.save(output_path, 'JPEG', quality=95)
        return True
        
    except Exception as e:
        print(f"Error: {e}")
        return False

def main():
    print("🔄 Image Converter ke 224x224 untuk MobileNetV2")
    print("=" * 50)
    
    # Direktori input dan output
    input_dir = input("📁 Masukkan path direktori input (atau tekan Enter untuk './data'): ").strip()
    if not input_dir:
        input_dir = "./data"
    
    output_dir = input("📁 Masukkan path direktori output (atau tekan Enter untuk './data_224x224'): ").strip()
    if not output_dir:
        output_dir = "./data_224x224"
    
    # Cek input directory
    if not os.path.exists(input_dir):
        print(f"❌ Direktori input tidak ditemukan: {input_dir}")
        print("\n📋 Buat struktur direktori seperti ini:")
        print("data/")
        print("  ├── Eurasian Tree Sparrow - Passer montanus/")
        print("  ├── Javan Munia - Lonchura leucogastroides/")
        print("  ├── Scaly-breasted Munia - Lonchura punctulata/")
        print("  └── White-headed Munia - Lonchura maja/")
        return
    
    # Buat output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Format gambar yang didukung
    image_formats = ('.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif')
    
    total_converted = 0
    total_failed = 0
    
    print(f"\n🔄 Memproses gambar dari: {input_dir}")
    print(f"💾 Hasil akan disimpan di: {output_dir}")
    print("-" * 50)
    
    # Proses setiap direktori kelas
    for class_name in os.listdir(input_dir):
        class_input_path = os.path.join(input_dir, class_name)
        
        if not os.path.isdir(class_input_path):
            continue
        
        print(f"\n📂 Processing: {class_name}")
        
        # Buat direktori output untuk kelas ini
        class_output_path = os.path.join(output_dir, class_name)
        os.makedirs(class_output_path, exist_ok=True)
        
        # Ambil semua file gambar
        image_files = [f for f in os.listdir(class_input_path) 
                      if f.lower().endswith(image_formats)]
        
        class_converted = 0
        class_failed = 0
        
        for i, filename in enumerate(image_files):
            input_file = os.path.join(class_input_path, filename)
            
            # Buat nama output file (selalu .jpg)
            name_without_ext = os.path.splitext(filename)[0]
            output_file = os.path.join(class_output_path, f"{name_without_ext}.jpg")
            
            # Konversi gambar
            if convert_image_with_padding(input_file, output_file):
                class_converted += 1
                total_converted += 1
            else:
                class_failed += 1
                total_failed += 1
                print(f"  ❌ Gagal: {filename}")
            
            # Progress
            if (i + 1) % 10 == 0 or (i + 1) == len(image_files):
                print(f"  Progress: {i + 1}/{len(image_files)}")
        
        print(f"  ✅ {class_name}: {class_converted} berhasil, {class_failed} gagal")
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 HASIL KONVERSI")
    print("=" * 50)
    print(f"✅ Total berhasil: {total_converted}")
    print(f"❌ Total gagal: {total_failed}")
    print(f"📊 Success rate: {(total_converted/(total_converted+total_failed)*100):.1f}%")
    print(f"\n💾 Gambar 224x224 tersimpan di: {output_dir}")
    
    if total_converted > 0:
        print("\n🚀 Siap untuk training MobileNetV2!")
        print("📋 Langkah selanjutnya:")
        print("1. Update path data di notebook ke:", output_dir)
        print("2. Jalankan training dengan data yang sudah dikonversi")

if __name__ == "__main__":
    main()
