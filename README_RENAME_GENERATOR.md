# 🔄 Rename Generator Scripts

Kumpulan script Python untuk melakukan rename file secara batch dengan berbagai format. Mendukung semua jenis file dan tidak ada batas jumlah file.

## 📁 File yang Tersedia

### 1. `rename_generator.py` - <PERSON><PERSON><PERSON> Utama (Lengkap)
Script paling fleksibel dengan banyak opsi dan mode interaktif.

**Fitur:**
- ✅ Mode command line dan interaktif
- ✅ Preview sebelum rename
- ✅ Custom nama dan nomor awal
- ✅ Error handling lengkap
- ✅ Mendukung semua jenis file
- ✅ Tidak ada batas jumlah file

### 2. `quick_rename.py` - Script Cepat
Script sederhana untuk rename cepat satu folder.

**Fitur:**
- ✅ Interface sederhana
- ✅ Pilihan folder preset
- ✅ Input manual
- ✅ Cocok untuk penggunaan sehari-hari

### 3. `batch_rename.py` - Script Batch
Script untuk rename semua folder dalam direktori sekaligus.

**Fitur:**
- ✅ Proses multiple folder sekaligus
- ✅ Preview mode
- ✅ Progress tracking
- ✅ Summary lengkap
- ✅ Opsi reset numbering per folder

### 4. `auto_rename_by_folder.py` - Auto Rename by Folder Name ⭐ **RECOMMENDED**
Script otomatis yang menggunakan nama folder sebagai base name untuk setiap folder.

**Fitur:**
- ✅ **Otomatis menggunakan nama folder masing-masing**
- ✅ Setiap folder punya nama file yang unik
- ✅ Preview mode dengan konfirmasi
- ✅ Single folder atau multiple folder
- ✅ Interface yang user-friendly

## 🚀 Cara Penggunaan

### Metode 1: Command Line (rename_generator.py)

```bash
# Rename folder Anisolabididae
python rename_generator.py "ResizedDatasetUnknown/Anisolabididae"

# Custom nama dan nomor awal
python rename_generator.py "path/to/folder" --name "Anisolabididae" --start 1

# Preview dulu sebelum rename
python rename_generator.py "path/to/folder" --preview

# Mulai dari nomor 100
python rename_generator.py "path/to/folder" --start 100
```

### Metode 2: Mode Interaktif (rename_generator.py)

```bash
# Jalankan tanpa parameter untuk mode interaktif
python rename_generator.py
```

Akan muncul prompt:
```
📁 Masukkan path folder: ResizedDatasetUnknown/Anisolabididae
🏷️  Nama baru (kosong = nama folder): Anisolabididae
🔢 Nomor awal (default: 1): 1
```

### Metode 3: Quick Rename (quick_rename.py)

```bash
python quick_rename.py
```

Pilih dari folder preset atau input manual.

### Metode 4: Auto Rename by Folder Name (auto_rename_by_folder.py) ⭐ **RECOMMENDED**

```bash
python auto_rename_by_folder.py
```

Script terbaik untuk rename otomatis berdasarkan nama folder masing-masing.

### Metode 5: Batch Rename (batch_rename.py)

```bash
python batch_rename.py
```

Untuk memproses semua folder dalam direktori sekaligus dengan opsi lanjutan.

## 📋 Contoh Output

### Sebelum Rename:
```
📁 ResizedDatasetUnknown/Anisolabididae/
├── IMG_001.jpg
├── photo_abc.png
├── image_xyz.jpeg
└── pic_123.jpg
```

### Setelah Rename:
```
📁 ResizedDatasetUnknown/Anisolabididae/
├── Anisolabididae 1.jpg
├── Anisolabididae 2.png
├── Anisolabididae 3.jpeg
└── Anisolabididae 4.jpg
```

## ⚙️ Parameter dan Opsi

### rename_generator.py
```bash
python rename_generator.py <folder> [options]

Options:
  --name, -n     : Nama baru untuk file (default: nama folder)
  --start, -s    : Nomor awal (default: 1)
  --preview, -p  : Preview saja, tidak melakukan rename
  --help, -h     : Tampilkan help
```

### Contoh Penggunaan Lanjutan

```bash
# Rename dengan nama custom
python rename_generator.py "Dataset/Unknown" --name "Sample" --start 1

# Preview untuk folder besar
python rename_generator.py "ResizedDataset" --preview

# Mulai dari nomor tertentu
python rename_generator.py "raw_data/birds" --start 501
```

## 🛡️ Fitur Keamanan

1. **Preview Mode**: Lihat hasil sebelum melakukan rename
2. **Backup Check**: Cek konflik nama file
3. **Error Handling**: Tangani error dengan baik
4. **Rollback**: Bisa dibatalkan dengan Ctrl+C
5. **Validation**: Validasi path dan input

## 📊 Format Output

Script akan menampilkan:
- ✅ File berhasil direname
- ❌ File gagal direname
- 📊 Summary (total berhasil/gagal)
- ⚠️ Warning dan error

## 🔧 Troubleshooting

### Error: "Folder tidak ditemukan"
- Pastikan path folder benar
- Gunakan tanda kutip jika ada spasi: `"path with spaces"`
- Cek apakah folder ada dengan `ls` atau `dir`

### Error: "File sudah ada"
- Ada file dengan nama yang sama
- Gunakan `--preview` untuk melihat konflik
- Hapus atau pindahkan file yang konflik

### Error: "Permission denied"
- File sedang dibuka di aplikasi lain
- Tutup aplikasi yang menggunakan file
- Jalankan sebagai administrator jika perlu

## 💡 Tips Penggunaan

1. **Selalu gunakan preview** untuk folder besar
2. **Backup data** sebelum rename massal
3. **Gunakan batch_rename.py** untuk multiple folder
4. **Gunakan quick_rename.py** untuk penggunaan cepat
5. **Cek hasil** setelah rename selesai

## 🎯 Use Cases

### Dataset Machine Learning
```bash
# Rename semua gambar dalam folder kelas
python rename_generator.py "Dataset/Anisolabididae" --name "Anisolabididae"
python rename_generator.py "Dataset/Carabidae" --name "Carabidae"
```

### Batch Processing
```bash
# Proses semua folder sekaligus
python batch_rename.py
# Pilih: ResizedDatasetUnknown
```

### Custom Numbering
```bash
# Mulai dari nomor tertentu
python rename_generator.py "folder" --start 1001
```

## 📝 Catatan

- Script mendukung semua format file (jpg, png, txt, dll.)
- Tidak ada batas jumlah file
- Urutan file berdasarkan nama (alphabetical)
- Ekstensi file tetap dipertahankan
- Nama folder digunakan sebagai default jika tidak ada input nama

## 🆘 Bantuan

Jika mengalami masalah:
1. Jalankan dengan `--preview` dulu
2. Cek path folder dengan benar
3. Pastikan tidak ada file yang sedang dibuka
4. Gunakan mode interaktif untuk kemudahan
