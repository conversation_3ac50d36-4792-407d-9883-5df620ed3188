#!/usr/bin/env python3
"""
Script untuk menjalankan URL Downloader Bot dengan berbagai opsi
"""

import argparse
import os
import sys
from url_downloader_bot import URLDownloaderBot

def main():
    parser = argparse.ArgumentParser(
        description="URL Downloader Bot - Download files from URLs in text file",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Download all files from multimedia.txt
  python run_downloader.py multimedia.txt

  # Download with custom output directory
  python run_downloader.py multimedia.txt -o my_images

  # Download first 50 files only
  python run_downloader.py multimedia.txt --limit 50

  # Use sequential download (slower but safer)
  python run_downloader.py multimedia.txt --sequential

  # Custom settings for slow connections
  python run_downloader.py multimedia.txt -w 2 -t 60 -d 2.0
        """
    )
    
    parser.add_argument("input_file", help="File containing URLs to download")
    parser.add_argument("-o", "--output", default="downloads", 
                       help="Output directory (default: downloads)")
    parser.add_argument("-w", "--workers", type=int, default=3, 
                       help="Number of parallel workers (default: 3)")
    parser.add_argument("-t", "--timeout", type=int, default=60, 
                       help="Request timeout in seconds (default: 60)")
    parser.add_argument("-r", "--retry", type=int, default=3, 
                       help="Number of retry attempts (default: 3)")
    parser.add_argument("-d", "--delay", type=float, default=1.0, 
                       help="Delay between downloads in seconds (default: 1.0)")
    parser.add_argument("--sequential", action="store_true", 
                       help="Use sequential download instead of parallel")
    parser.add_argument("--limit", type=int, 
                       help="Limit number of files to download (for testing)")
    
    args = parser.parse_args()
    
    # Validate input file
    if not os.path.exists(args.input_file):
        print(f"Error: Input file '{args.input_file}' not found!")
        sys.exit(1)
    
    # Print configuration
    print("="*60)
    print("URL DOWNLOADER BOT")
    print("="*60)
    print(f"Input file: {args.input_file}")
    print(f"Output directory: {args.output}")
    print(f"Workers: {args.workers}")
    print(f"Timeout: {args.timeout}s")
    print(f"Retry attempts: {args.retry}")
    print(f"Delay: {args.delay}s")
    print(f"Mode: {'Sequential' if args.sequential else 'Parallel'}")
    if args.limit:
        print(f"Limit: {args.limit} files")
    print("="*60)
    
    # Create bot instance
    bot = URLDownloaderBot(
        input_file=args.input_file,
        output_dir=args.output,
        max_workers=args.workers,
        timeout=args.timeout,
        retry_count=args.retry,
        delay_between_downloads=args.delay
    )
    
    try:
        # Get URLs
        urls = bot.extract_urls_from_file()
        
        if not urls:
            print("No valid URLs found in the input file!")
            sys.exit(1)
        
        # Apply limit if specified
        if args.limit and args.limit > 0:
            urls = urls[:args.limit]
            print(f"Limited to first {len(urls)} URLs")
        
        print(f"Found {len(urls)} URLs to download")
        
        # Set total for statistics
        bot.stats['total'] = len(urls)
        
        # Download files
        if args.sequential or args.workers == 1:
            bot.download_sequential([(url, filename) for url, filename in urls])
        else:
            bot.download_parallel([(url, filename) for url, filename in urls])
        
        bot.print_summary()
        
        print(f"\nDownload completed! Files saved to: {args.output}")
        
    except KeyboardInterrupt:
        print("\n\nDownload interrupted by user!")
        bot.print_summary()
    except Exception as e:
        print(f"Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
