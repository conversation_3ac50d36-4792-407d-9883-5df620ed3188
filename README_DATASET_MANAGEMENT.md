# 📊 Dataset Management - Panduan <PERSON>

Koleksi script Python untuk management dataset machine learning dengan pembagian training/validation yang adil dan konsisten.

## 📁 File yang Tersedia

### 1. `dataset_splitter.py` - Script Utama Pembagi Dataset
Script utama untuk membagi dataset dengan proporsi 70:30 atau custom.

### 2. `run_dataset_split.py` - Interface User-Friendly ⭐ **RECOMMENDED**
Script wrapper dengan interface yang mudah digunakan.

### 3. `dataset_utils.py` - Utilitas Dataset
Script untuk validasi, analisis, dan maintenance dataset.

### 4. `README_DATASET_SPLITTER.md` - Dokumentasi Detail
Dokumentasi lengkap untuk dataset splitter.

## 🚀 Quick Start (Recommended)

### Langkah 1: Jalankan Script Utama
```bash
python run_dataset_split.py
```

### Langkah 2: Pilih Opsi
```
🚀 RUN DATASET SPLIT
============================================================
1. 📊 Pembagian Standar (70:30, seed=42)
2. ⚙️  Pembagian Custom (ratio dan parameter custom)  
3. 📈 Analisis Dataset yang sudah ada
4. 🔍 Cek Source Dataset
5. ❌ Keluar

Pilih opsi (1-5): 1
```

### Langkah 3: Konfirmasi dan Jalankan
Script akan:
1. ✅ Cek source dataset (`ResizedDataset/Dataset`)
2. 🔍 Tampilkan preview pembagian
3. ❓ Minta konfirmasi
4. 🔄 Jalankan pembagian
5. 📊 Tampilkan analisis hasil

## 📊 Hasil Pembagian

### Struktur Output:
```
SplitDataset/
├── train/                          (70% dari setiap kelas)
│   ├── Lonchura leucogastroides/   (350 files)
│   ├── Lonchura maja/              (280 files)
│   ├── Lonchura punctulata/        (210 files)
│   ├── Passer montanus/            (140 files)
│   ├── Anisolabididae/             (455 files)
│   ├── Carabidae/                  (455 files)
│   ├── Coccinellidae/              (455 files)
│   ├── Hirundo rustica/            (140 files)
│   ├── Ictinaetus malaiensis/      (140 files)
│   ├── Paddy/                      (455 files)
│   ├── Pycnonotus aurigaster/      (140 files)
│   ├── Staphylinidae/              (455 files)
│   └── Todiramphus chloris/        (140 files)
└── validation/                     (30% dari setiap kelas)
    ├── Lonchura leucogastroides/   (150 files)
    ├── Lonchura maja/              (120 files)
    └── ... (semua kelas)
```

### Statistik Hasil:
- **Total Files**: 5,850
- **Training**: 4,095 files (70.0%)
- **Validation**: 1,755 files (30.0%)
- **Classes**: 13 kelas
- **Pembagian**: Adil untuk setiap kelas

## 🛠️ Opsi Penggunaan

### 1. Pembagian Standar (Recommended)
```bash
python run_dataset_split.py
# Pilih opsi 1
```
- ✅ Ratio: 70:30
- ✅ Seed: 42 (konsisten)
- ✅ Output: `SplitDataset/`
- ✅ Preview otomatis

### 2. Pembagian Custom
```bash
python run_dataset_split.py
# Pilih opsi 2
```
- ⚙️ Custom ratio (contoh: 80:20)
- ⚙️ Custom output directory
- ⚙️ Custom random seed
- ⚙️ Preview otomatis

### 3. Analisis Dataset
```bash
python run_dataset_split.py
# Pilih opsi 3
```
- 📊 Validasi struktur
- 📊 Statistik lengkap
- 📊 Detail per kelas
- 📊 Ratio analysis

### 4. Direct Command Line
```bash
# Quick split dengan default
python dataset_splitter.py quick

# Custom split
python dataset_splitter.py

# Analisis
python dataset_splitter.py analyze SplitDataset
```

## 📈 Contoh Output

### Cek Source Dataset:
```
✅ Source dataset ditemukan: /path/to/ResizedDataset/Dataset
📁 Ditemukan 13 kelas:
   📁 Anisolabididae: 650 files
   📁 Carabidae: 650 files
   📁 Coccinellidae: 650 files
   📁 Hirundo rustica: 200 files
   📁 Ictinaetus malaiensis: 200 files
   📁 Lonchura leucogastroides: 500 files
   📁 Lonchura maja: 400 files
   📁 Lonchura punctulata: 300 files
   📁 Paddy: 650 files
   📁 Passer montanus: 200 files
   📁 Pycnonotus aurigaster: 200 files
   📁 Staphylinidae: 650 files
   📁 Todiramphus chloris: 200 files
📊 Total files: 5,850
```

### Preview Pembagian:
```
🔍 PREVIEW PEMBAGIAN:
----------------------------------------
📁 Ditemukan 13 kelas:
  📁 Anisolabididae: 650 files → Train: 455, Val: 195
  📁 Carabidae: 650 files → Train: 455, Val: 195
  📁 Coccinellidae: 650 files → Train: 455, Val: 195
  📁 Hirundo rustica: 200 files → Train: 140, Val: 60
  📁 Ictinaetus malaiensis: 200 files → Train: 140, Val: 60
  📁 Lonchura leucogastroides: 500 files → Train: 350, Val: 150
  📁 Lonchura maja: 400 files → Train: 280, Val: 120
  📁 Lonchura punctulata: 300 files → Train: 210, Val: 90
  📁 Paddy: 650 files → Train: 455, Val: 195
  📁 Passer montanus: 200 files → Train: 140, Val: 60
  📁 Pycnonotus aurigaster: 200 files → Train: 140, Val: 60
  📁 Staphylinidae: 650 files → Train: 455, Val: 195
  📁 Todiramphus chloris: 200 files → Train: 140, Val: 60

📊 Total keseluruhan: 5,850 files
📊 Total Train: 4,095 files (70.0%)
📊 Total Validation: 1,755 files (30.0%)
```

### Analisis Hasil:
```
📊 DATASET SUMMARY: SplitDataset
============================================================
✅ Dataset structure: VALID
📁 Total Classes: 13
📊 Total Files: 5,850
📚 Training: 4,095 files (70.0%)
📝 Validation: 1,755 files (30.0%)

📋 Detail per kelas:
  📁 Anisolabididae: 650 total → Train: 455 (70.0%), Val: 195 (30.0%)
  📁 Carabidae: 650 total → Train: 455 (70.0%), Val: 195 (30.0%)
  📁 Coccinellidae: 650 total → Train: 455 (70.0%), Val: 195 (30.0%)
  📁 Hirundo rustica: 200 total → Train: 140 (70.0%), Val: 60 (30.0%)
  📁 Ictinaetus malaiensis: 200 total → Train: 140 (70.0%), Val: 60 (30.0%)
  📁 Lonchura leucogastroides: 500 total → Train: 350 (70.0%), Val: 150 (30.0%)
  📁 Lonchura maja: 400 total → Train: 280 (70.0%), Val: 120 (30.0%)
  📁 Lonchura punctulata: 300 total → Train: 210 (70.0%), Val: 90 (30.0%)
  📁 Paddy: 650 total → Train: 455 (70.0%), Val: 195 (30.0%)
  📁 Passer montanus: 200 total → Train: 140 (70.0%), Val: 60 (30.0%)
  📁 Pycnonotus aurigaster: 200 total → Train: 140 (70.0%), Val: 60 (30.0%)
  📁 Staphylinidae: 650 total → Train: 455 (70.0%), Val: 195 (30.0%)
  📁 Todiramphus chloris: 200 total → Train: 140 (70.0%), Val: 60 (30.0%)
```

## 🔧 Utilitas Tambahan

### Validasi Dataset
```bash
python dataset_utils.py
# Pilih opsi 1: Validate dataset structure
```

### Generate Report
```bash
python dataset_utils.py
# Pilih opsi 2: Generate dataset report
```

### Cleanup
```bash
python dataset_utils.py
# Pilih opsi 4: Cleanup empty directories
```

### Compare Datasets
```bash
python dataset_utils.py
# Pilih opsi 5: Compare two datasets
```

## 💡 Tips dan Best Practices

### 1. Sebelum Pembagian
- ✅ Pastikan dataset sudah di-resize (224x224)
- ✅ Pastikan file sudah di-rename dengan konsisten
- ✅ Backup dataset asli jika perlu

### 2. Pemilihan Ratio
- **70:30** - Standard untuk dataset besar (>1000 per kelas)
- **80:20** - Untuk dataset sedang (500-1000 per kelas)
- **60:40** - Untuk dataset kecil (<500 per kelas)

### 3. Random Seed
- Gunakan seed yang sama untuk reproducibility
- Seed default (42) sudah teruji
- Ganti seed jika ingin variasi pembagian

### 4. Validasi Hasil
- Selalu cek hasil dengan analisis
- Pastikan semua kelas terbagi dengan benar
- Verifikasi ratio sesuai target

## 🚨 Troubleshooting

### Error: "Source dataset tidak ditemukan"
```bash
# Pastikan Anda di direktori yang benar
pwd
ls -la ResizedDataset/Dataset/
```

### Error: "Dataset structure tidak valid"
```bash
# Cek struktur dengan utilitas
python dataset_utils.py
# Pilih opsi 1: Validate
```

### Error: "Permission denied"
```bash
# Jalankan dengan permission yang tepat
chmod +x *.py
# Atau jalankan sebagai admin jika perlu
```

### Hasil tidak konsisten
- Pastikan menggunakan seed yang sama
- Jangan mengubah file di source selama proses
- Gunakan Python version yang sama

## 📊 Integrasi dengan Training

### TensorFlow/Keras
```python
import tensorflow as tf

# Load dataset
train_ds = tf.keras.preprocessing.image_dataset_from_directory(
    'SplitDataset/train',
    image_size=(224, 224),
    batch_size=32
)

val_ds = tf.keras.preprocessing.image_dataset_from_directory(
    'SplitDataset/validation',
    image_size=(224, 224),
    batch_size=32
)

# Training
model.fit(train_ds, validation_data=val_ds, epochs=10)
```

### PyTorch
```python
from torchvision import datasets, transforms
from torch.utils.data import DataLoader

transform = transforms.Compose([
    transforms.Resize((224, 224)),
    transforms.ToTensor(),
])

train_dataset = datasets.ImageFolder('SplitDataset/train', transform=transform)
val_dataset = datasets.ImageFolder('SplitDataset/validation', transform=transform)

train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
val_loader = DataLoader(val_dataset, batch_size=32, shuffle=False)
```

## 📝 Summary

Script dataset management ini menyediakan:

1. **Pembagian Adil**: Setiap kelas dibagi dengan proporsi yang sama
2. **Konsistensi**: Random seed memastikan hasil yang dapat direproduksi
3. **Fleksibilitas**: Support custom ratio dan parameter
4. **Validasi**: Cek struktur dan integritas dataset
5. **Analisis**: Statistik lengkap dan detail per kelas
6. **User-Friendly**: Interface yang mudah digunakan

**Recommended workflow:**
1. Jalankan `python run_dataset_split.py`
2. Pilih "Pembagian Standar" (opsi 1)
3. Konfirmasi hasil
4. Gunakan `SplitDataset/` untuk training

Dataset Anda siap untuk training machine learning! 🚀
