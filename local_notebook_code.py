# ===== KODE UNTUK NOTEBOOK - COPY PASTE KE CELL NOTEBOOK ANDA =====

# Import libraries untuk environment lokal
import tensorflow as tf
from tensorflow.keras.layers import GlobalAveragePooling2D, Dropout, Dense
from tensorflow.keras.optimizers import <PERSON>
from tensorflow.keras.applications import MobileNetV2
from tensorflow.keras.models import Model
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from sklearn.utils.class_weight import compute_class_weight
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix
import os
import cv2
from PIL import Image
from pathlib import Path

print("✅ Libraries imported successfully for local environment!")
print(f"TensorFlow version: {tf.__version__}")
print(f"GPU available: {len(tf.config.list_physical_devices('GPU'))} GPU(s)")

# ===== DETEKSI DAN KONFIGURASI DATA LOKAL =====

print("\n🔍 Mendeteksi dataset yang tersedia...")

# Prioritas direktori data (dari yang paling diutamakan)
data_options = [
    ('./ResizedDataset', 'Data yang sudah diproses ke 224x224 - RECOMMENDED'),
    ('./Dataset', 'Data asli dengan berbagai ukuran'),
    ('./data', 'Direktori data alternatif'),
    ('./processed_data', 'Data hasil preprocessing')
]

selected_data_dir = None
dataset_info = {}

# Cek setiap opsi direktori
for data_dir, description in data_options:
    if os.path.exists(data_dir):
        print(f"\n📁 Ditemukan: {data_dir}")
        print(f"   📝 {description}")
        
        # Cek subdirectory dan hitung gambar
        subdirs = []
        total_images = 0
        
        for item in os.listdir(data_dir):
            item_path = os.path.join(data_dir, item)
            if os.path.isdir(item_path):
                # Hitung gambar di kelas ini
                image_files = [f for f in os.listdir(item_path) 
                             if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff'))]
                if image_files:  # Hanya tambahkan jika ada gambar
                    subdirs.append({
                        'name': item,
                        'count': len(image_files)
                    })
                    total_images += len(image_files)
        
        if subdirs:  # Jika ada kelas dengan gambar
            print(f"   📊 {len(subdirs)} kelas, {total_images} gambar total")
            print(f"   📋 Kelas yang ditemukan:")
            for subdir in subdirs:
                print(f"      - {subdir['name']}: {subdir['count']} gambar")
            
            # Gunakan direktori pertama yang valid
            if not selected_data_dir:
                selected_data_dir = data_dir
                dataset_info = {
                    'classes': subdirs,
                    'total_images': total_images,
                    'num_classes': len(subdirs)
                }
                print(f"\n✅ DIPILIH: {data_dir}")
        else:
            print(f"   ⚠️  Tidak ada kelas dengan gambar yang valid")

# Hasil deteksi dan konfigurasi final
if selected_data_dir:
    main_data_dir = selected_data_dir
    num_classes = dataset_info['num_classes']
    
    print(f"\n" + "="*60)
    print(f"🎯 KONFIGURASI DATA FINAL")
    print(f"="*60)
    print(f"📁 Data directory: {main_data_dir}")
    print(f"📊 Jumlah kelas: {num_classes}")
    print(f"📊 Total gambar: {dataset_info['total_images']}")
    print(f"🔧 Batch size: 32 (disesuaikan untuk environment lokal)")
    print(f"📊 Validation split: 30%")
    
    print(f"\n✅ Dataset siap digunakan!")
    
else:
    print(f"\n❌ TIDAK DITEMUKAN DATASET YANG VALID!")
    print(f"\n📋 Silakan pastikan salah satu direktori berikut ada dan berisi data:")
    for data_dir, description in data_options:
        print(f"  - {data_dir} ({description})")
    print(f"\n📁 Struktur direktori yang diharapkan:")
    print(f"Dataset/ (atau ResizedDataset/)")
    print(f"  ├── Lonchura leucogastroides/")
    print(f"  ├── Lonchura maja/")
    print(f"  ├── Lonchura punctulata/")
    print(f"  └── Passer montanus/")
    print(f"\nSetiap subdirektori harus berisi gambar dari spesies burung yang sesuai.")
    
    # Fallback
    main_data_dir = './Dataset'  # Default fallback
    num_classes = 4  # Default untuk 4 kelas burung
    print(f"\n⚠️  Menggunakan fallback: {main_data_dir}")

# ===== KONFIGURASI MODEL =====

# Define the input shape
input_shape = (224, 224, 3)

# Batch size disesuaikan untuk environment lokal
batch_size = 32

print(f"\n🏗️  Konfigurasi model:")
print(f"📊 Input shape: {input_shape}")
print(f"📊 Number of classes: {num_classes}")
print(f"🔧 Batch size: {batch_size}")

# ===== DATA GENERATORS =====

print(f"\n🔄 Membuat data generators...")

# Data generator dengan augmentasi untuk training
train_datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=20,
    width_shift_range=0.2,
    height_shift_range=0.2,
    shear_range=0.2,
    zoom_range=0.2,
    horizontal_flip=True,
    vertical_flip=True,
    fill_mode='nearest',
    validation_split=0.3,
)

# Data generator untuk validation (hanya rescaling)
valid_datagen = ImageDataGenerator(
    rescale=1./255,
    validation_split=0.3
)

# Cek apakah direktori data ada sebelum membuat generators
if os.path.exists(main_data_dir):
    print(f"✅ Creating data generators from: {main_data_dir}")
    
    # Define train and validation generators
    train_generator = train_datagen.flow_from_directory(
        main_data_dir,
        target_size=(224, 224),
        batch_size=batch_size,
        class_mode='categorical',
        shuffle=True,
        subset="training"
    )
    
    valid_generator = valid_datagen.flow_from_directory(
        main_data_dir,
        target_size=(224, 224),
        batch_size=batch_size,
        class_mode='categorical',
        shuffle=False,
        subset="validation"
    )
    
    print(f"\n✅ Data generators berhasil dibuat!")
    print(f"📊 Training samples: {train_generator.samples}")
    print(f"📊 Validation samples: {valid_generator.samples}")
    print(f"📊 Number of classes: {train_generator.num_classes}")
    print(f"📊 Class indices: {train_generator.class_indices}")
    
    # Update num_classes berdasarkan data generator yang sebenarnya
    num_classes = train_generator.num_classes
    print(f"🔄 Updated num_classes to: {num_classes}")
    
else:
    print(f"❌ Data directory tidak ditemukan: {main_data_dir}")
    print("Silakan pastikan data sudah tersedia sebelum melanjutkan.")

print(f"\n" + "="*60)
print(f"✅ SETUP DATA LOKAL SELESAI!")
print(f"="*60)
print(f"📋 Variabel yang tersedia:")
print(f"  - main_data_dir: {main_data_dir}")
print(f"  - num_classes: {num_classes}")
print(f"  - batch_size: {batch_size}")
print(f"  - input_shape: {input_shape}")
print(f"  - train_generator: Ready")
print(f"  - valid_generator: Ready")
print(f"\n🚀 Anda sekarang bisa melanjutkan dengan membuat model MobileNetV2!")
