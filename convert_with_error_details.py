#!/usr/bin/env python3
"""
Script konversi gambar ke 224x224 dengan detail error yang lengkap
Menampilkan nama file yang gagal dan alasan kegagalannya
"""

import os
import cv2
import numpy as np
from PIL import Image
import datetime

def convert_image_with_details(input_path, output_path):
    """
    Konversi gambar dengan detail error yang lengkap
    """
    filename = os.path.basename(input_path)
    
    try:
        # Cek apakah file ada
        if not os.path.exists(input_path):
            return False, f"File tidak ditemukan: {filename}"
        
        # Cek ukuran file
        file_size = os.path.getsize(input_path)
        if file_size == 0:
            return False, f"File kosong (0 bytes): {filename}"
        
        if file_size < 100:
            return False, f"File terlalu kecil ({file_size} bytes): {filename}"
        
        # Baca gambar dengan OpenCV
        image = cv2.imread(input_path)
        if image is None:
            # Coba dengan PIL jika OpenCV gagal
            try:
                with Image.open(input_path) as pil_img:
                    # Convert PIL ke OpenCV format
                    if pil_img.mode != 'RGB':
                        pil_img = pil_img.convert('RGB')
                    image = cv2.cvtColor(np.array(pil_img), cv2.COLOR_RGB2BGR)
            except Exception as pil_error:
                return False, f"Tidak bisa dibaca (OpenCV & PIL gagal): {filename} - {str(pil_error)}"
        
        # Validasi ukuran gambar
        if image is None or len(image.shape) != 3:
            return False, f"Format gambar tidak valid: {filename}"
        
        height, width = image.shape[:2]
        if width <= 0 or height <= 0:
            return False, f"Ukuran tidak valid ({width}x{height}): {filename}"
        
        # Cek apakah gambar terlalu kecil
        if width < 10 or height < 10:
            return False, f"Gambar terlalu kecil ({width}x{height}): {filename}"
        
        # Hitung scale untuk fit ke 224x224
        scale = min(224 / width, 224 / height)
        
        # Ukuran baru
        new_width = int(width * scale)
        new_height = int(height * scale)
        
        # Validasi ukuran baru
        if new_width <= 0 or new_height <= 0:
            return False, f"Ukuran hasil resize tidak valid ({new_width}x{new_height}): {filename}"
        
        # Resize gambar
        resized = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)
        
        # Buat canvas 224x224 hitam
        result = np.zeros((224, 224, 3), dtype=np.uint8)
        
        # Hitung posisi untuk center gambar
        x_offset = (224 - new_width) // 2
        y_offset = (224 - new_height) // 2
        
        # Paste gambar ke center
        result[y_offset:y_offset+new_height, x_offset:x_offset+new_width] = resized
        
        # Buat direktori output jika belum ada
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Save gambar
        success = cv2.imwrite(output_path, result)
        if not success:
            return False, f"Gagal menyimpan file: {filename}"
        
        return True, f"✅ {filename} ({width}x{height} → 224x224)"
        
    except Exception as e:
        return False, f"Error tidak terduga pada {filename}: {str(e)}"

def convert_with_detailed_logging(input_dir, output_dir):
    """
    Konversi dengan logging detail
    """
    if not os.path.exists(input_dir):
        print(f"❌ Direktori input tidak ditemukan: {input_dir}")
        return
    
    # Buat output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Statistik
    stats = {
        'total': 0,
        'success': 0,
        'failed': 0,
        'success_files': [],
        'failed_files': [],
        'class_stats': {}
    }
    
    print(f"🔄 KONVERSI GAMBAR KE 224x224")
    print(f"{'='*60}")
    print(f"📁 Input: {input_dir}")
    print(f"📁 Output: {output_dir}")
    print(f"🕐 Started: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"{'='*60}")
    
    # Proses setiap kelas
    for class_name in sorted(os.listdir(input_dir)):
        class_input_path = os.path.join(input_dir, class_name)
        
        if not os.path.isdir(class_input_path):
            continue
        
        print(f"\n📂 Processing: {class_name}")
        print("-" * 50)
        
        # Buat output directory untuk kelas
        class_output_path = os.path.join(output_dir, class_name)
        os.makedirs(class_output_path, exist_ok=True)
        
        # Statistik per kelas
        class_stats = {'success': 0, 'failed': 0, 'files': []}
        
        # Get semua file gambar
        image_files = [f for f in os.listdir(class_input_path) 
                      if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'))]
        
        print(f"📊 Found {len(image_files)} image files")
        
        # Proses setiap gambar
        for i, filename in enumerate(image_files, 1):
            input_file = os.path.join(class_input_path, filename)
            
            # Output filename (selalu .jpg)
            name = os.path.splitext(filename)[0]
            output_file = os.path.join(class_output_path, f"{name}.jpg")
            
            stats['total'] += 1
            
            # Konversi gambar
            success, message = convert_image_with_details(input_file, output_file)
            
            if success:
                stats['success'] += 1
                class_stats['success'] += 1
                stats['success_files'].append(f"{class_name}/{filename}")
                print(f"  {i:3d}. {message}")
            else:
                stats['failed'] += 1
                class_stats['failed'] += 1
                stats['failed_files'].append({
                    'class': class_name,
                    'filename': filename,
                    'path': input_file,
                    'error': message
                })
                print(f"  {i:3d}. ❌ {message}")
            
            class_stats['files'].append({
                'filename': filename,
                'success': success,
                'message': message
            })
        
        # Summary per kelas
        print(f"\n📊 {class_name} Summary:")
        print(f"  ✅ Success: {class_stats['success']}")
        print(f"  ❌ Failed: {class_stats['failed']}")
        print(f"  📊 Success rate: {(class_stats['success']/(class_stats['success']+class_stats['failed'])*100):.1f}%")
        
        stats['class_stats'][class_name] = class_stats
    
    # Final summary
    print(f"\n{'='*60}")
    print("📊 FINAL SUMMARY")
    print(f"{'='*60}")
    print(f"📈 Total files processed: {stats['total']}")
    print(f"✅ Successfully converted: {stats['success']}")
    print(f"❌ Failed to convert: {stats['failed']}")
    print(f"📊 Overall success rate: {(stats['success']/stats['total']*100):.1f}%" if stats['total'] > 0 else "0%")
    print(f"🕐 Completed: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Detail failed files
    if stats['failed_files']:
        print(f"\n❌ FAILED FILES DETAILS ({len(stats['failed_files'])} files):")
        print("-" * 60)
        
        for i, failed in enumerate(stats['failed_files'], 1):
            print(f"{i:3d}. {failed['class']}/{failed['filename']}")
            print(f"     Error: {failed['error']}")
            print(f"     Path: {failed['path']}")
            print()
        
        # Save failed files report
        report_file = f"failed_conversion_report_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("FAILED CONVERSION REPORT\n")
            f.write("=" * 50 + "\n")
            f.write(f"Generated: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Input Directory: {input_dir}\n")
            f.write(f"Output Directory: {output_dir}\n")
            f.write(f"Total Failed: {len(stats['failed_files'])}\n\n")
            
            for i, failed in enumerate(stats['failed_files'], 1):
                f.write(f"{i}. {failed['class']}/{failed['filename']}\n")
                f.write(f"   Error: {failed['error']}\n")
                f.write(f"   Full Path: {failed['path']}\n\n")
        
        print(f"💾 Failed files report saved to: {report_file}")
    
    else:
        print(f"\n🎉 ALL FILES CONVERTED SUCCESSFULLY!")
    
    print(f"\n📁 Converted images saved to: {output_dir}")
    
    return stats

def main():
    print("🔄 Image Converter 224x224 dengan Detail Error")
    print("=" * 60)
    
    # Input paths
    input_dir = input("📁 Input directory (Enter untuk './data'): ").strip()
    if not input_dir:
        input_dir = "./data"
    
    output_dir = input("📁 Output directory (Enter untuk './data_224x224'): ").strip()
    if not output_dir:
        output_dir = "./data_224x224"
    
    # Konfirmasi
    print(f"\n📋 Configuration:")
    print(f"  Input: {input_dir}")
    print(f"  Output: {output_dir}")
    
    confirm = input("\n❓ Lanjutkan konversi? (y/n): ").lower().strip()
    if confirm != 'y':
        print("❌ Konversi dibatalkan.")
        return
    
    # Jalankan konversi
    stats = convert_with_detailed_logging(input_dir, output_dir)
    
    if stats and stats['failed'] > 0:
        print(f"\n💡 Tips untuk mengatasi file yang gagal:")
        print("1. Cek apakah file benar-benar gambar")
        print("2. Coba buka file dengan image viewer")
        print("3. Convert file ke format JPG/PNG")
        print("4. Hapus file yang corrupt")
        print("5. Jalankan ulang script ini")

if __name__ == "__main__":
    main()
