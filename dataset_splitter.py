#!/usr/bin/env python3
"""
Dataset Splitter - Pembagi Dataset untuk Folder Tunggal dan Multi-Class
Script untuk membagi dataset dari satu folder atau struktur multi-class menjadi set training dan testing.
Proporsi default adalah 70% untuk training dan 30% untuk testing.

Mendukung:
1. Single folder: Semua gambar dalam satu folder
2. Multi-class: Struktur folder dengan subfolder untuk setiap kelas
"""

import os
import shutil
from pathlib import Path
import random
import math

def detect_dataset_structure(source_dir):
    """
    Mendeteksi struktur dataset: single folder atau multi-class.

    Args:
        source_dir (str): Path ke direktori sumber.

    Returns:
        tuple: (structure_type, class_info)
            - structure_type: 'single' atau 'multi_class'
            - class_info: dict dengan informasi kelas (untuk multi_class) atau None (untuk single)
    """
    source_path = Path(source_dir)

    if not source_path.is_dir():
        return None, None

    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
    direct_images = list(set(f for ext in image_extensions for f in source_path.glob(f'*{ext}')))

    subfolders_with_images = {}
    for item in source_path.iterdir():
        if item.is_dir():
            subfolder_images = list(set(f for ext in image_extensions for f in item.glob(f'*{ext}')))
            if subfolder_images:
                subfolders_with_images[item.name] = len(subfolder_images)

    if direct_images and not subfolders_with_images:
        return 'single', {'total_images': len(direct_images)}
    elif subfolders_with_images and not direct_images:
        return 'multi_class', subfolders_with_images
    elif subfolders_with_images and direct_images:
        print("⚠️ Ditemukan gambar di root dan di subfolder. Akan diproses sebagai multi-class.")
        return 'multi_class', subfolders_with_images
    else:
        return None, None

def create_train_test_split_single(source_dir, output_dir, train_ratio=0.7, random_seed=42, preview=False):
    """
    Membagi dataset dari satu folder menjadi training dan testing.

    Args:
        source_dir (str): Path ke direktori sumber yang berisi semua file gambar.
        output_dir (str): Path ke direktori output (akan dibuatkan folder 'train' dan 'test').
        train_ratio (float): Proporsi untuk data training (default: 0.7 = 70%).
        random_seed (int): Seed untuk random generator demi konsistensi.
        preview (bool): Jika True, hanya menampilkan preview tanpa menyalin file.

    Returns:
        dict: Ringkasan hasil pembagian.
    """
    source_path = Path(source_dir)
    output_path = Path(output_dir)

    if not source_path.is_dir():
        print(f"❌ Error: Direktori sumber '{source_dir}' tidak ditemukan atau bukan direktori!")
        return None

    random.seed(random_seed)
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff', '*.webp']
    all_files = list(set(f for ext in image_extensions for f in source_path.glob(ext)))

    if not all_files:
        print(f"❌ Error: Tidak ada file gambar yang ditemukan di '{source_dir}'!")
        return None

    all_files.sort(key=lambda x: x.name.lower())
    random.shuffle(all_files)

    total_count = len(all_files)
    train_count = math.floor(total_count * train_ratio)
    test_count = total_count - train_count
    train_files = all_files[:train_count]
    test_files = all_files[train_count:]

    print("🔄 DATASET SPLITTER (SINGLE FOLDER)")
    print("=" * 60)
    print(f"📁 Sumber: {source_path}")
    print(f"📁 Output: {output_path}")
    print(f"📊 Rasio Train: {train_ratio*100:.0f}%")
    print(f"📊 Rasio Test: {(1-train_ratio)*100:.0f}%")
    print(f"🎲 Random Seed: {random_seed}")
    print(f"🖼️ Total File Ditemukan: {total_count}")
    print("-" * 60)
    print(f"📚 Akan dibagi menjadi Train: {train_count} files")
    print(f"📝 Akan dibagi menjadi Test: {test_count} files")

    if preview:
        print("\n🔍 MODE: PREVIEW (Tidak ada file yang akan disalin)")
        print("=" * 60)
        print(f"Contoh file untuk Training (3 file pertama):")
        for file_path in train_files[:3]:
            print(f"   → train/{file_path.name}")
        if len(train_files) > 3: print("   → ... dan seterusnya")

        print(f"\nContoh file untuk Test (3 file pertama):")
        for file_path in test_files[:3]:
            print(f"   → test/{file_path.name}")
        if len(test_files) > 3: print("   → ... dan seterusnya")
    else:
        print("\n🔄 MODE: COPY FILES")
        confirm = input(f"\n❓ Lanjutkan menyalin {total_count} file ke '{output_path}'? (y/n): ").strip().lower()
        if confirm not in ['y', 'yes', 'ya']:
            print("❌ Proses penyalinan dibatalkan.")
            return None

        train_dir = output_path / "train"
        test_dir = output_path / "test"
        train_dir.mkdir(parents=True, exist_ok=True)
        test_dir.mkdir(parents=True, exist_ok=True)

        print("\n" + "=" * 60)
        print("🚀 Memulai proses penyalinan...")

        print(f"\n📚 Menyalin {len(train_files)} file ke '{train_dir}'...")
        for file_path in train_files:
            shutil.copy2(file_path, train_dir / file_path.name)

        print(f"📝 Menyalin {len(test_files)} file ke '{test_dir}'...")
        for file_path in test_files:
            shutil.copy2(file_path, test_dir / file_path.name)

        print("\n" + "=" * 60)
        print("🎉 PEMBAGIAN DATASET SELESAI!")
        print(f"   ✅ Total file disalin: {total_count}")
        print(f"   📁 Direktori Output: {output_path}")
        print(f"   📁 Direktori Train: {train_dir} ({len(train_files)} files)")
        print(f"   📁 Direktori Test: {test_dir} ({len(test_files)} files)")

    return {
        'total_files': total_count,
        'total_train': train_count,
        'total_test': test_count,
        'output_dir': str(output_path) if not preview else None
    }

def create_train_test_split_multiclass(source_dir, output_dir, train_ratio=0.7, random_seed=42, preview=False):
    """
    Membagi dataset multi-class menjadi training dan testing dengan mempertahankan struktur kelas.
    """
    source_path = Path(source_dir)
    output_path = Path(output_dir)

    if not source_path.is_dir():
        print(f"❌ Error: Direktori sumber '{source_dir}' tidak ditemukan atau bukan direktori!")
        return None

    random.seed(random_seed)
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
    class_data = {}
    total_files = 0

    for class_folder in source_path.iterdir():
        if class_folder.is_dir():
            class_name = class_folder.name
            class_files = list(set(f for ext in image_extensions for f in class_folder.glob(f'*{ext}')))

            if class_files:
                class_files.sort(key=lambda x: x.name.lower())
                random.shuffle(class_files)
                
                class_total = len(class_files)
                class_train_count = math.floor(class_total * train_ratio)
                class_test_count = class_total - class_train_count

                class_data[class_name] = {
                    'total': class_total,
                    'train_files': class_files[:class_train_count],
                    'test_files': class_files[class_train_count:],
                    'train_count': class_train_count,
                    'test_count': class_test_count
                }
                total_files += class_total

    if not class_data:
        print(f"❌ Error: Tidak ada kelas dengan file gambar yang ditemukan di '{source_dir}'!")
        return None

    total_train = sum(data['train_count'] for data in class_data.values())
    total_test = sum(data['test_count'] for data in class_data.values())

    print("🔄 DATASET SPLITTER (MULTI-CLASS)")
    print("=" * 60)
    print(f"📁 Sumber: {source_path}")
    print(f"📁 Output: {output_path}")
    print(f"📊 Rasio Train: {train_ratio*100:.0f}%")
    print(f"📊 Rasio Test: {(1-train_ratio)*100:.0f}%")
    print(f"🎲 Random Seed: {random_seed}")
    print(f"🖼️ Total File Ditemukan: {total_files}")
    print(f"📂 Jumlah Kelas: {len(class_data)}")
    print("-" * 60)
    print("📊 Distribusi per kelas:")
    for class_name, data in class_data.items():
        print(f"   📂 {class_name}: {data['total']} files → Train: {data['train_count']}, Test: {data['test_count']}")
    print("-" * 60)
    print(f"📚 Total Train: {total_train} files")
    print(f"📝 Total Test: {total_test} files")

    if preview:
        print("\n🔍 MODE: PREVIEW (Tidak ada file yang akan disalin)")
        print("=" * 60)
        print("Struktur output yang akan dibuat:")
        print(f"{output_path}/")
        print("├── train/")
        for class_name in class_data.keys(): print(f"│   ├── {class_name}/")
        print("└── test/")
        for class_name in class_data.keys(): print(f"     ├── {class_name}/")
    else:
        print("\n🔄 MODE: COPY FILES")
        confirm = input(f"\n❓ Lanjutkan menyalin {total_files} file ke '{output_path}'? (y/n): ").strip().lower()
        if confirm not in ['y', 'yes', 'ya']:
            print("❌ Proses penyalinan dibatalkan.")
            return None

        train_dir = output_path / "train"
        test_dir = output_path / "test"
        train_dir.mkdir(parents=True, exist_ok=True)
        test_dir.mkdir(parents=True, exist_ok=True)

        print("\n" + "=" * 60)
        print("🚀 Memulai proses penyalinan...")

        for class_name, data in class_data.items():
            print(f"\n📂 Memproses kelas: {class_name}")
            train_class_dir = train_dir / class_name
            test_class_dir = test_dir / class_name
            train_class_dir.mkdir(exist_ok=True)
            test_class_dir.mkdir(exist_ok=True)

            print(f"   📚 Menyalin {len(data['train_files'])} file ke train/{class_name}/")
            for file_path in data['train_files']:
                shutil.copy2(file_path, train_class_dir / file_path.name)

            print(f"   📝 Menyalin {len(data['test_files'])} file ke test/{class_name}/")
            for file_path in data['test_files']:
                shutil.copy2(file_path, test_class_dir / file_path.name)

        print("\n" + "=" * 60)
        print("🎉 PEMBAGIAN DATASET MULTI-CLASS SELESAI!")
        print(f"   ✅ Total file disalin: {total_files}")
        print(f"   📁 Direktori Output: {output_path}")
        print(f"   📂 Jumlah Kelas: {len(class_data)}")
        print(f"   📚 Total Train: {total_train} files")
        print(f"   📝 Total Test: {total_test} files")

    return {
        'total_files': total_files,
        'total_train': total_train,
        'total_test': total_test,
        'num_classes': len(class_data),
        'output_dir': str(output_path) if not preview else None
    }

def create_train_test_split(source_dir, output_dir, train_ratio=0.7, random_seed=42, preview=False):
    """
    Fungsi wrapper yang mendeteksi struktur dataset dan memanggil fungsi yang sesuai.
    """
    structure_type, class_info = detect_dataset_structure(source_dir)

    if structure_type is None:
        print(f"❌ Error: Tidak dapat mendeteksi struktur dataset di '{source_dir}'!")
        print("Pastikan direktori berisi file gambar atau subfolder dengan file gambar.")
        return None

    print(f"🔍 Struktur dataset terdeteksi: {structure_type.upper()}")

    if structure_type == 'single':
        print(f"📊 Total gambar ditemukan: {class_info['total_images']}")
        return create_train_test_split_single(source_dir, output_dir, train_ratio, random_seed, preview)
    elif structure_type == 'multi_class':
        total_images = sum(class_info.values())
        print(f"📊 Total kelas ditemukan: {len(class_info)}")
        print(f"📊 Total gambar ditemukan: {total_images}")
        print("📂 Distribusi kelas:")
        for class_name, count in class_info.items():
            print(f"   - {class_name}: {count} gambar")
        return create_train_test_split_multiclass(source_dir, output_dir, train_ratio, random_seed, preview)
    else:
        print(f"❌ Error: Struktur dataset tidak dikenali!")
        return None

def analyze_dataset(dataset_dir):
    """
    Menganalisis dataset yang sudah dibagi (mendukung single folder dan multi-class).
    """
    dataset_path = Path(dataset_dir)
    if not dataset_path.exists():
        print(f"❌ Direktori dataset '{dataset_dir}' tidak ditemukan!")
        return

    train_dir = dataset_path / "train"
    test_dir = dataset_path / "test"

    if not train_dir.exists() or not test_dir.exists():
        print(f"❌ Direktori 'train' atau 'test' tidak ditemukan di dalam '{dataset_dir}'!")
        return

    print(f"📊 ANALISIS DATASET: {dataset_dir}")
    print("=" * 60)

    train_subdirs = [d for d in train_dir.iterdir() if d.is_dir()]
    test_subdirs = [d for d in test_dir.iterdir() if d.is_dir()]

    total_train = 0
    total_test = 0

    if train_subdirs and test_subdirs:
        print("🔍 Struktur: MULTI-CLASS")
        print("-" * 60)
        train_class_counts = {d.name: len(list(d.glob('*'))) for d in train_subdirs}
        test_class_counts = {d.name: len(list(d.glob('*'))) for d in test_subdirs}
        all_classes = sorted(set(train_class_counts.keys()) | set(test_class_counts.keys()))

        print("📂 Distribusi per kelas:")
        for class_name in all_classes:
            train_count = train_class_counts.get(class_name, 0)
            test_count = test_class_counts.get(class_name, 0)
            class_total = train_count + test_count
            if class_total > 0:
                train_pct = (train_count / class_total) * 100
                test_pct = (test_count / class_total) * 100
                print(f"   📂 {class_name}: {class_total} files → Train: {train_count} ({train_pct:.1f}%), Test: {test_count} ({test_pct:.1f}%)")
            total_train += train_count
            total_test += test_count
        print("-" * 60)
        print(f"📂 Jumlah Kelas: {len(all_classes)}")
    else:
        print("🔍 Struktur: SINGLE FOLDER")
        print("-" * 60)
        total_train = len([f for f in train_dir.glob('*') if f.is_file()])
        total_test = len([f for f in test_dir.glob('*') if f.is_file()])

    total_files = total_train + total_test
    if total_files == 0:
        print("❌ Dataset kosong.")
        return

    train_pct = (total_train / total_files) * 100
    test_pct = (total_test / total_files) * 100

    print(f"📚 TRAINING SET : {total_train} files ({train_pct:.1f}%)")
    print(f"📝 TEST SET     : {total_test} files ({test_pct:.1f}%)")
    print(f"📊 TOTAL        : {total_files} files")

def main():
    print("🔄 DATASET SPLITTER (SINGLE FOLDER & MULTI-CLASS)")
    print("=" * 60)
    print("Script ini akan membagi dataset menjadi set 'train' dan 'test'.")
    print()
    print("Struktur output yang akan dibuat:")
    print("   output_dir/")
    print("   ├── train/")
    print("   └── test/")
    print()

    default_source = "Dataset"
    default_output = "SplitDataset"
    
    source_dir = input(f"📁 Masukkan path direktori sumber (default: {default_source}): ").strip() or default_source
    output_dir = input(f"📁 Masukkan path direktori output (default: {default_output}): ").strip() or default_output
    
    try:
        train_ratio_input = input("📊 Masukkan rasio training (mis: 0.7 untuk 70%, default: 0.7): ").strip()
        train_ratio = float(train_ratio_input) if train_ratio_input else 0.7
        if not 0.1 <= train_ratio <= 0.9:
            print("⚠️ Rasio harus antara 0.1 dan 0.9. Menggunakan default 0.7.")
            train_ratio = 0.7
    except ValueError:
        print("⚠️ Input tidak valid. Menggunakan default 0.7.")
        train_ratio = 0.7
    
    try:
        seed_input = input("🎲 Masukkan random seed (angka integer, default: 42): ").strip()
        random_seed = int(seed_input) if seed_input else 42
    except ValueError:
        print("⚠️ Seed tidak valid. Menggunakan default 42.")
        random_seed = 42
    
    print("\n" + "="*60)
    result = create_train_test_split(source_dir, output_dir, train_ratio, random_seed, preview=True)
    
    if result is None:
        return
    
    print("\n" + "="*60)
    confirm_copy = input("❓ Preview terlihat benar. Lanjutkan dengan menyalin file? (y/n): ").strip().lower()
    if confirm_copy in ['y', 'yes', 'ya']:
        create_train_test_split(source_dir, output_dir, train_ratio, random_seed, preview=False)
    else:
        print("❌ Pembagian dataset dibatalkan oleh pengguna.")

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        if sys.argv[1] == "analyze":
            dataset_dir = sys.argv[2] if len(sys.argv) > 2 else "SplitDataset"
            analyze_dataset(dataset_dir)
        elif sys.argv[1] == "help":
            print("🔄 DATASET SPLITTER - BANTUAN")
            print("=" * 60)
            print("Penggunaan:")
            print("   python nama_script.py                # Mode interaktif")
            print("   python nama_script.py analyze [folder] # Analisis dataset")
            print("   python nama_script.py help           # Tampilkan bantuan")
            print()
            print("Output akan dibuat dalam struktur 'train'/'test':")
            print("   SplitDataset/       (Contoh output_dir)")
            print("   ├── train/")
            print("   │   └── (folder kelas jika multi-class)")
            print("   └── test/")
            print("       └── (folder kelas jika multi-class)")
        else:
            print(f"❌ Perintah tidak dikenal: {sys.argv[1]}")
            print("Gunakan 'python nama_script.py help' untuk bantuan.")
    else:
        main()