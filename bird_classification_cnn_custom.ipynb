{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Bird Classification with Custom CNN\n", "## Klasifikasi Burung menggunakan Custom CNN Architecture\n", "\n", "Notebook ini mengimplementasikan klasifikasi burung menggunakan arsitektur CNN custom yang terinspirasi dari notebook klasifikasi tanaman melon, tetapi disesuaikan untuk dataset burung."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Import Libraries dan Setup Environment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install dependencies jika diperlukan\n", "# !pip install tensorflow matplotlib seaborn pillow scikit-learn\n", "\n", "import os\n", "import sys\n", "import shutil\n", "import pathlib\n", "import natsort\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import tensorflow as tf\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "from PIL import Image\n", "from tqdm import tqdm\n", "from tensorflow.keras.preprocessing import image\n", "from tensorflow.keras.preprocessing.image import ImageDataGenerator\n", "from tensorflow.keras.models import Sequential\n", "from tensorflow.keras.layers import Conv2D, MaxPooling2D, Dropout, Flatten, Dense\n", "from tensorflow.keras.optimizers import Adam\n", "from tensorflow.keras.callbacks import ModelCheckpoint, EarlyStopping\n", "\n", "from sklearn.metrics import (\n", "    accuracy_score,\n", "    precision_score,\n", "    recall_score,\n", "    confusion_matrix,\n", "    classification_report\n", ")\n", "\n", "print(\"✅ Libraries imported successfully!\")\n", "print(f\"TensorFlow version: {tf.__version__}\")\n", "print(f\"GPU available: {tf.config.list_physical_devices('GPU')}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Dataset Configuration dan <PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ===== KONFIGURASI DATASET BURUNG =====\n", "print(\"🔍 Mendeteksi dataset burung yang tersedia...\")\n", "\n", "# Prioritas direktori data (dari yang paling diutamakan)\n", "data_options = [\n", "    ('./ResizedDataset', 'Data yang sudah diproses ke 224x224 - RECOMMENDED'),\n", "    ('./Dataset', 'Data asli dengan berbagai ukuran'),\n", "    ('./SplitDataset/train', 'Data yang sudah di-split untuk training'),\n", "    ('./data', 'Direktori data alternatif')\n", "]\n", "\n", "selected_data_dir = None\n", "dataset_info = {}\n", "\n", "# Ce<PERSON> setiap opsi direktori\n", "for data_dir, description in data_options:\n", "    if os.path.exists(data_dir):\n", "        print(f\"\\n📁 Ditemukan: {data_dir}\")\n", "        print(f\"   📝 {description}\")\n", "        \n", "        # Cek subdirectory dan hitung gambar\n", "        subdirs = []\n", "        total_images = 0\n", "        \n", "        for item in os.listdir(data_dir):\n", "            item_path = os.path.join(data_dir, item)\n", "            if os.path.isdir(item_path):\n", "                # Hitung gambar di kelas ini\n", "                image_files = [f for f in os.listdir(item_path) \n", "                             if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff'))])\n", "                if image_files:  # <PERSON><PERSON> tambahkan jika ada gambar\n", "                    subdirs.append({\n", "                        'name': item,\n", "                        'count': len(image_files)\n", "                    })\n", "                    total_images += len(image_files)\n", "        \n", "        if subdirs:  # <PERSON><PERSON> ada kelas dengan gambar\n", "            print(f\"   📊 {len(subdirs)} kelas, {total_images} gambar total\")\n", "            print(f\"   📋 Kelas yang ditemukan:\")\n", "            for subdir in subdirs:\n", "                print(f\"      - {subdir['name']}: {subdir['count']} gambar\")\n", "            \n", "            # Gunakan direktori pertama yang valid\n", "            if not selected_data_dir:\n", "                selected_data_dir = data_dir\n", "                dataset_info = {\n", "                    'classes': subdirs,\n", "                    'total_images': total_images,\n", "                    'num_classes': len(subdirs)\n", "                }\n", "                print(f\"\\n✅ DIPILIH: {data_dir}\")\n", "\n", "# <PERSON><PERSON> dete<PERSON>i\n", "if selected_data_dir:\n", "    main_data_dir = selected_data_dir\n", "    num_classes = dataset_info['num_classes']\n", "    \n", "    print(f\"\\n\" + \"=\"*60)\n", "    print(f\"🎯 KONFIGURASI DATA FINAL\")\n", "    print(f\"=\"*60)\n", "    print(f\"📁 Data directory: {main_data_dir}\")\n", "    print(f\"📊 Jumlah kelas: {num_classes}\")\n", "    print(f\"📊 Total gambar: {dataset_info['total_images']}\")\n", "    print(f\"🔧 Batch size: 16 (disesuaikan untuk CNN custom)\")\n", "    print(f\"📊 Validation split: 20%\")\n", "    \n", "    # Buat mapping kelas burung\n", "    bird_classes = [cls['name'] for cls in dataset_info['classes']]\n", "    print(f\"\\n🐦 Kelas burung: {bird_classes}\")\n", "    \n", "else:\n", "    print(f\"\\n❌ TIDAK DITEMUKAN DATASET YANG VALID!\")\n", "    # Fallback\n", "    main_data_dir = './Dataset'\n", "    num_classes = 4\n", "    bird_classes = ['Lonchura leucogastroides', 'Lonchura maja', 'Lonchura punctulata', '<PERSON>er montanus']"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Data Augmentation dan Preview"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ===== DATA AUGMENTATION SETUP =====\n", "print(\"🔄 Setting up data augmentation...\")\n", "\n", "# Buat direktori preview jika belum ada\n", "preview_dir = './preview_augmentation'\n", "os.makedirs(preview_dir, exist_ok=True)\n", "\n", "# Ambil sample gambar untuk preview augmentasi\n", "sample_found = False\n", "for class_name in bird_classes:\n", "    class_path = os.path.join(main_data_dir, class_name)\n", "    if os.path.exists(class_path):\n", "        image_files = [f for f in os.listdir(class_path) \n", "                      if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff'))])\n", "        if image_files:\n", "            sample_image_path = os.path.join(class_path, image_files[0])\n", "            sample_found = True\n", "            break\n", "\n", "if sample_found:\n", "    # Load dan konversi gambar ke array untuk preview\n", "    img_augmentation = image.load_img(sample_image_path, target_size=(224, 224))\n", "    x_aug = image.img_to_array(img_augmentation)\n", "    x_aug = x_aug.reshape((1,) + x_aug.shape)\n", "    \n", "    # Setup augmentasi yang disesuaikan untuk burung\n", "    preview_datagen = ImageDataGenerator(\n", "        rescale=1./255,\n", "        rotation_range=15,        # <PERSON><PERSON><PERSON> lebih kecil untuk burung\n", "        width_shift_range=0.1,    # Shift lebih kecil\n", "        height_shift_range=0.1,\n", "        shear_range=0.1,          # <PERSON><PERSON> lebih kecil\n", "        zoom_range=0.15,          # Zoom lebih kecil\n", "        horizontal_flip=True,     # Flip horizontal OK untuk burung\n", "        vertical_flip=False,      # Tidak flip vertikal untuk burung\n", "        fill_mode='nearest'\n", "    )\n", "    \n", "    # Generate preview augmentasi\n", "    print(f\"📸 Generating augmentation preview from: {sample_image_path}\")\n", "    i = 0\n", "    for batch in preview_datagen.flow(x_aug, batch_size=1, \n", "                                     save_to_dir=preview_dir, \n", "                                     save_prefix='bird_aug', \n", "                                     save_format='jpeg'):\n", "        i += 1\n", "        if i >= 12:  # Generate 12 preview images\n", "            break\n", "    \n", "    # <PERSON><PERSON><PERSON><PERSON> hasil preview\n", "    preview_images = [f for f in os.listdir(preview_dir) if f.startswith('bird_aug')]\n", "    \n", "    if preview_images:\n", "        plt.figure(figsize=(15, 10))\n", "        for n, img_name in enumerate(preview_images[:12]):\n", "            plt.subplot(3, 4, n + 1)\n", "            plt.subplots_adjust(hspace=0.3, wspace=0.2)\n", "            \n", "            img = image.load_img(os.path.join(preview_dir, img_name),\n", "                               target_size=(224, 224))\n", "            plt.imshow(img)\n", "            plt.title(f'Augmented {n+1}', fontsize=10)\n", "            plt.axis('off')\n", "        \n", "        plt.suptitle('🐦 Bird Image Augmentation Preview', fontsize=16, fontweight='bold')\n", "        plt.tight_layout()\n", "        plt.show()\n", "        \n", "        print(f\"✅ Preview augmentasi berhasil dibuat!\")\n", "    \n", "else:\n", "    print(\"⚠️ Tidak dapat menemukan sample gambar untuk preview\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Data Generators Setup"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ===== DATA GENERATORS =====\n", "print(\"🔄 Creating data generators...\")\n", "\n", "# Data generator untuk training dengan augmentasi\n", "train_datagen = ImageDataGenerator(\n", "    rescale=1./255,\n", "    rotation_range=15,\n", "    width_shift_range=0.1,\n", "    height_shift_range=0.1,\n", "    shear_range=0.1,\n", "    zoom_range=0.15,\n", "    horizontal_flip=True,\n", "    vertical_flip=False,  # Tidak flip vertikal untuk burung\n", "    fill_mode='nearest',\n", "    validation_split=0.2  # 20% untuk validasi\n", ")\n", "\n", "# Data generator untuk validasi (hanya rescaling)\n", "val_datagen = ImageDataGenerator(\n", "    rescale=1./255,\n", "    validation_split=0.2\n", ")\n", "\n", "# Batch size disesuaikan untuk CNN custom\n", "batch_size = 16\n", "target_size = (224, 224)\n", "\n", "# Training generator\n", "train_generator = train_datagen.flow_from_directory(\n", "    main_data_dir,\n", "    target_size=target_size,\n", "    batch_size=batch_size,\n", "    class_mode='categorical',\n", "    subset='training',\n", "    shuffle=True\n", ")\n", "\n", "# Validation generator\n", "validation_generator = val_datagen.flow_from_directory(\n", "    main_data_dir,\n", "    target_size=target_size,\n", "    batch_size=batch_size,\n", "    class_mode='categorical',\n", "    subset='validation',\n", "    shuffle=False\n", ")\n", "\n", "print(f\"\\n✅ Data generators berhasil dibuat!\")\n", "print(f\"📊 Training samples: {train_generator.samples}\")\n", "print(f\"📊 Validation samples: {validation_generator.samples}\")\n", "print(f\"📊 Number of classes: {train_generator.num_classes}\")\n", "print(f\"📊 Class indices: {train_generator.class_indices}\")\n", "print(f\"🔧 Batch size: {batch_size}\")\n", "print(f\"📐 Target size: {target_size}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Custom CNN Architecture"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ===== CUSTOM CNN ARCHITECTURE =====\n", "print(\"🏗️ Building Custom CNN Architecture...\")\n", "\n", "# Arsitektur CNN yang disesuaikan untuk klasifikasi burung\n", "model = Sequential([\n", "    # Block 1\n", "    Conv2D(32, (3, 3), activation='relu', padding='same', input_shape=(224, 224, 3)),\n", "    Conv2D(32, (3, 3), activation='relu', padding='same'),\n", "    MaxPooling2D((2, 2)),\n", "    Dropout(0.25),\n", "    \n", "    # Block 2\n", "    Conv2D(64, (3, 3), activation='relu', padding='same'),\n", "    Conv2D(64, (3, 3), activation='relu', padding='same'),\n", "    MaxPooling2D((2, 2)),\n", "    Dropout(0.25),\n", "    \n", "    # Block 3\n", "    Conv2D(128, (3, 3), activation='relu', padding='same'),\n", "    Conv2D(128, (3, 3), activation='relu', padding='same'),\n", "    MaxPooling2D((2, 2)),\n", "    Dropout(0.25),\n", "    \n", "    # Block 4\n", "    Conv2D(256, (3, 3), activation='relu', padding='same'),\n", "    Conv2D(256, (3, 3), activation='relu', padding='same'),\n", "    MaxPooling2D((2, 2)),\n", "    Dropout(0.25),\n", "    \n", "    # Classifier\n", "    <PERSON><PERSON>(),\n", "    Dense(512, activation='relu'),\n", "    Dropout(0.5),\n", "    Dense(256, activation='relu'),\n", "    Dropout(0.5),\n", "    Dense(num_classes, activation='softmax')\n", "])\n", "\n", "# Compile model\n", "model.compile(\n", "    optimizer=<PERSON>(learning_rate=0.001),\n", "    loss='categorical_crossentropy',\n", "    metrics=['accuracy']\n", ")\n", "\n", "# Display model summary\n", "print(\"\\n📋 Model Architecture Summary:\")\n", "model.summary()\n", "\n", "# Hitung total parameter\n", "total_params = model.count_params()\n", "print(f\"\\n📊 Total Parameters: {total_params:,}\")\n", "print(f\"📊 Model size estimate: ~{total_params * 4 / (1024*1024):.1f} MB\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Training Configuration dan <PERSON>backs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ===== TRAINING CONFIGURATION =====\n", "print(\"⚙️ Setting up training configuration...\")\n", "\n", "# Buat direktori untuk menyimpan model\n", "model_dir = './models'\n", "os.makedirs(model_dir, exist_ok=True)\n", "\n", "# Model checkpoint callback\n", "checkpoint = ModelCheckpoint(\n", "    filepath=os.path.join(model_dir, 'best_bird_cnn_model.h5'),\n", "    monitor='val_accuracy',\n", "    mode='max',\n", "    save_best_only=True,\n", "    save_weights_only=False,\n", "    verbose=1\n", ")\n", "\n", "# Early stopping callback\n", "early_stopping = EarlyStopping(\n", "    monitor='val_loss',\n", "    min_delta=0.001,\n", "    patience=15,\n", "    verbose=1,\n", "    restore_best_weights=True\n", ")\n", "\n", "# Custom callback untuk stop training pada a<PERSON>si tertentu\n", "class AccuracyThresholdCallback(tf.keras.callbacks.Callback):\n", "    def __init__(self, threshold=0.95):\n", "        super(<PERSON>ccura<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.threshold = threshold\n", "    \n", "    def on_epoch_end(self, epoch, logs=None):\n", "        if logs.get('val_accuracy') > self.threshold:\n", "            print(f\"\\n🎯 Reached {self.threshold*100}% validation accuracy! Stopping training.\")\n", "            self.model.stop_training = True\n", "\n", "accuracy_callback = AccuracyThresholdCallback(threshold=0.92)\n", "\n", "# Learning rate scheduler\n", "def scheduler(epoch, lr):\n", "    if epoch < 10:\n", "        return lr\n", "    else:\n", "        return lr * tf.math.exp(-0.1)\n", "\n", "lr_scheduler = tf.keras.callbacks.LearningRateScheduler(scheduler, verbose=1)\n", "\n", "# <PERSON><PERSON><PERSON> all callbacks\n", "callbacks = [checkpoint, early_stopping, accuracy_callback, lr_scheduler]\n", "\n", "print(\"✅ Training configuration ready!\")\n", "print(f\"📁 Model will be saved to: {os.path.join(model_dir, 'best_bird_cnn_model.h5')}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Model Training"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ===== MODEL TRAINING =====\n", "print(\"🚀 Starting model training...\")\n", "\n", "# Calculate steps per epoch\n", "steps_per_epoch = max(1, train_generator.samples // batch_size)\n", "validation_steps = max(1, validation_generator.samples // batch_size)\n", "\n", "print(f\"📊 Steps per epoch: {steps_per_epoch}\")\n", "print(f\"📊 Validation steps: {validation_steps}\")\n", "\n", "# Train the model\n", "history = model.fit(\n", "    train_generator,\n", "    steps_per_epoch=steps_per_epoch,\n", "    epochs=50,  # Maximum epochs\n", "    validation_data=validation_generator,\n", "    validation_steps=validation_steps,\n", "    callbacks=callbacks,\n", "    verbose=1\n", ")\n", "\n", "print(\"\\n🎉 Training completed!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Training History Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ===== TRAINING HISTORY VISUALIZATION =====\n", "print(\"📊 Visualizing training history...\")\n", "\n", "# Extract training history\n", "acc = history.history['accuracy']\n", "val_acc = history.history['val_accuracy']\n", "loss = history.history['loss']\n", "val_loss = history.history['val_loss']\n", "epochs_range = range(len(acc))\n", "\n", "# Create subplots\n", "plt.figure(figsize=(15, 5))\n", "\n", "# Accuracy plot\n", "plt.subplot(1, 3, 1)\n", "plt.plot(epochs_range, acc, label='Training Accuracy', linewidth=2)\n", "plt.plot(epochs_range, val_acc, label='Validation Accuracy', linewidth=2)\n", "plt.title('🎯 Training and Validation Accuracy', fontsize=14, fontweight='bold')\n", "plt.xlabel('Epochs')\n", "plt.ylabel('Accuracy')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "# Loss plot\n", "plt.subplot(1, 3, 2)\n", "plt.plot(epochs_range, loss, label='Training Loss', linewidth=2)\n", "plt.plot(epochs_range, val_loss, label='Validation Loss', linewidth=2)\n", "plt.title('📉 Training and Validation Loss', fontsize=14, fontweight='bold')\n", "plt.xlabel('Epochs')\n", "plt.ylabel('Loss')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "# Learning rate plot (if available)\n", "plt.subplot(1, 3, 3)\n", "if 'lr' in history.history:\n", "    plt.plot(epochs_range, history.history['lr'], label='Learning Rate', linewidth=2, color='orange')\n", "    plt.title('📈 Learning Rate Schedule', fontsize=14, fontweight='bold')\n", "    plt.xlabel('Epochs')\n", "    plt.ylabel('Learning Rate')\n", "    plt.yscale('log')\n", "    plt.legend()\n", "    plt.grid(True, alpha=0.3)\n", "else:\n", "    # Show final metrics instead\n", "    final_acc = acc[-1]\n", "    final_val_acc = val_acc[-1]\n", "    final_loss = loss[-1]\n", "    final_val_loss = val_loss[-1]\n", "    \n", "    metrics_text = f\"Final Metrics:\\n\\n\"\n", "    metrics_text += f\"Training Accuracy: {final_acc:.4f}\\n\"\n", "    metrics_text += f\"Validation Accuracy: {final_val_acc:.4f}\\n\\n\"\n", "    metrics_text += f\"Training Loss: {final_loss:.4f}\\n\"\n", "    metrics_text += f\"Validation Loss: {final_val_loss:.4f}\"\n", "    \n", "    plt.text(0.1, 0.5, metrics_text, fontsize=12, \n", "             bbox=dict(boxstyle=\"round,pad=0.3\", facecolor=\"lightblue\", alpha=0.7),\n", "             transform=plt.gca().transAxes)\n", "    plt.title('📊 Final Training Metrics', fontsize=14, fontweight='bold')\n", "    plt.axis('off')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Save training history\n", "results_dir = './results'\n", "os.makedirs(results_dir, exist_ok=True)\n", "\n", "# Save as CSV\n", "history_df = pd.DataFrame(history.history)\n", "history_df.to_csv(os.path.join(results_dir, 'training_history.csv'), index=False)\n", "\n", "print(f\"✅ Training history saved to: {os.path.join(results_dir, 'training_history.csv')}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Feature Map Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ===== FEATURE MAP VISUALIZATION =====\n", "print(\"🔍 Creating feature map visualization...\")\n", "\n", "# Get indices of convolutional layers\n", "conv_layer_indices = []\n", "conv_layer_names = []\n", "\n", "for i, layer in enumerate(model.layers):\n", "    if isinstance(layer, Conv2D):\n", "        conv_layer_indices.append(i)\n", "        conv_layer_names.append(layer.name)\n", "\n", "print(f\"📋 Found {len(conv_layer_indices)} convolutional layers:\")\n", "for i, name in enumerate(conv_layer_names):\n", "    print(f\"   {i+1}. {name} (index: {conv_layer_indices[i]})\")\n", "\n", "# Create models to extract feature maps from first few conv layers\n", "feature_layers = [model.layers[i].output for i in conv_layer_indices[:4]]  # First 4 conv layers\n", "feature_model = tf.keras.Model(inputs=model.input, outputs=feature_layers)\n", "\n", "# Function to visualize feature maps\n", "def visualize_feature_maps(model, img_path, layer_names, n_features=8):\n", "    \"\"\"Visualize feature maps from convolutional layers\"\"\"\n", "    # Load and preprocess image\n", "    img = image.load_img(img_path, target_size=(224, 224))\n", "    img_array = image.img_to_array(img)\n", "    img_array = np.expand_dims(img_array, axis=0) / 255.0\n", "    \n", "    # Get feature maps\n", "    feature_maps = model.predict(img_array)\n", "    \n", "    # Plot original image and feature maps\n", "    n_layers = len(feature_maps)\n", "    fig, axes = plt.subplots(n_layers + 1, n_features + 1, figsize=(20, 4 * (n_layers + 1)))\n", "    \n", "    # Show original image\n", "    axes[0, 0].imshow(img)\n", "    axes[0, 0].set_title('Original Image', fontweight='bold')\n", "    axes[0, 0].axis('off')\n", "    \n", "    # Hide unused subplots in first row\n", "    for j in range(1, n_features + 1):\n", "        axes[0, j].axis('off')\n", "    \n", "    # Show feature maps for each layer\n", "    for i, (feature_map, layer_name) in enumerate(zip(feature_maps, layer_names)):\n", "        # Show layer info\n", "        axes[i + 1, 0].text(0.5, 0.5, f'{layer_name}\\nShape: {feature_map.shape[1:]}', \n", "                           ha='center', va='center', fontsize=10, fontweight='bold',\n", "                           bbox=dict(boxstyle=\"round,pad=0.3\", facecolor=\"lightgray\"))\n", "        axes[i + 1, 0].axis('off')\n", "        \n", "        # Show feature maps\n", "        for j in range(min(n_features, feature_map.shape[-1])):\n", "            axes[i + 1, j + 1].imshow(feature_map[0, :, :, j], cmap='viridis')\n", "            axes[i + 1, j + 1].set_title(f'Filter {j+1}', fontsize=8)\n", "            axes[i + 1, j + 1].axis('off')\n", "    \n", "    plt.suptitle(f'🔍 Feature Maps Visualization - {os.path.basename(img_path)}', \n", "                 fontsize=16, fontweight='bold')\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Find a sample image for visualization\n", "sample_image = None\n", "for class_name in bird_classes:\n", "    class_path = os.path.join(main_data_dir, class_name)\n", "    if os.path.exists(class_path):\n", "        image_files = [f for f in os.listdir(class_path) \n", "                      if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff'))][:1]\n", "        if image_files:\n", "            sample_image = os.path.join(class_path, image_files[0])\n", "            break\n", "\n", "if sample_image and len(conv_layer_indices) >= 4:\n", "    print(f\"📸 Visualizing feature maps for: {sample_image}\")\n", "    visualize_feature_maps(feature_model, sample_image, conv_layer_names[:4], n_features=6)\n", "else:\n", "    print(\"⚠️ Could not create feature map visualization\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}