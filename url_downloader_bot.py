#!/usr/bin/env python3
"""
URL Downloader Bot
Bot untuk mendownload file dari URLs yang ada dalam file text.
Mendukung format CSV dan text biasa.
"""

import os
import sys
import csv
import requests
import time
import argparse
from urllib.parse import urlparse, unquote
from pathlib import Path
import logging
from typing import List, Optional, Tuple
import re
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm

class URLDownloaderBot:
    def __init__(self, 
                 input_file: str,
                 output_dir: str = "downloads",
                 max_workers: int = 5,
                 timeout: int = 30,
                 retry_count: int = 3,
                 delay_between_downloads: float = 0.5):
        """
        Initialize URL Downloader Bot
        
        Args:
            input_file: Path ke file yang berisi URLs
            output_dir: Directory untuk menyimpan file yang didownload
            max_workers: Jumlah thread untuk download paralel
            timeout: Timeout untuk setiap request (detik)
            retry_count: Jumlah retry jika download gagal
            delay_between_downloads: Delay antar download (detik)
        """
        self.input_file = input_file
        self.output_dir = Path(output_dir)
        self.max_workers = max_workers
        self.timeout = timeout
        self.retry_count = retry_count
        self.delay_between_downloads = delay_between_downloads
        
        # Setup logging
        self.setup_logging()
        
        # Create output directory
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Statistics
        self.stats = {
            'total': 0,
            'success': 0,
            'failed': 0,
            'skipped': 0
        }
    
    def setup_logging(self):
        """Setup logging configuration"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('downloader.log'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def extract_urls_from_file(self) -> List[Tuple[str, str]]:
        """
        Extract URLs from input file
        Returns list of tuples (url, suggested_filename)
        """
        urls = []
        
        try:
            with open(self.input_file, 'r', encoding='utf-8') as file:
                # Try to detect if it's CSV
                sample = file.read(1024)
                file.seek(0)
                
                if self.is_csv_format(sample):
                    urls = self.extract_from_csv(file)
                else:
                    urls = self.extract_from_text(file)
                    
        except Exception as e:
            self.logger.error(f"Error reading file {self.input_file}: {e}")
            return []
        
        self.logger.info(f"Found {len(urls)} URLs in {self.input_file}")
        return urls
    
    def is_csv_format(self, sample: str) -> bool:
        """Check if the file appears to be CSV format"""
        # Check for common CSV indicators (comma or tab separated)
        has_separator = (',' in sample or '\t' in sample)
        has_headers = ('identifier' in sample.lower() or 'gbifid' in sample.lower())
        has_multiple_lines = sample.count('\n') > 1

        return has_separator and has_headers and has_multiple_lines
    
    def extract_from_csv(self, file) -> List[Tuple[str, str]]:
        """Extract URLs from CSV file"""
        urls = []
        csv_reader = csv.DictReader(file, delimiter='\t')  # Tab-separated based on your file

        for row_num, row in enumerate(csv_reader, 1):
            # Only look for URL in 'identifier' column for multimedia files
            url = None
            if 'identifier' in row and row['identifier']:
                potential_url = row['identifier'].strip()
                # Only take URLs that look like direct file downloads
                if self.is_downloadable_url(potential_url):
                    url = potential_url
                    # Generate filename from gbifID if available, otherwise from URL
                    filename = self.generate_filename(url, row.get('gbifID', ''), row_num)
                    urls.append((url, filename))

        return urls
    
    def extract_from_text(self, file) -> List[Tuple[str, str]]:
        """Extract URLs from plain text file"""
        urls = []
        url_pattern = re.compile(r'https?://[^\s<>"]+')
        
        for line_num, line in enumerate(file, 1):
            found_urls = url_pattern.findall(line.strip())
            for url in found_urls:
                if self.is_valid_url(url):
                    filename = self.generate_filename(url, '', line_num)
                    urls.append((url, filename))
        
        return urls
    
    def is_valid_url(self, url: str) -> bool:
        """Check if URL is valid"""
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except:
            return False

    def is_downloadable_url(self, url: str) -> bool:
        """Check if URL points to a downloadable file (not a webpage)"""
        if not url or not isinstance(url, str):
            return False

        url_lower = url.strip().lower()

        # Skip non-http URLs
        if not url_lower.startswith(('http://', 'https://')):
            return False

        # Skip license URLs and other non-file URLs
        skip_patterns = [
            'creativecommons.org',
            'licenses/',
        ]

        for pattern in skip_patterns:
            if pattern in url_lower:
                return False

        # Only accept direct file URLs with extensions or from specific file hosting services
        file_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.tiff', '.bmp', '.svg']
        has_file_extension = any(url_lower.endswith(ext) or f'{ext}?' in url_lower for ext in file_extensions)

        # Accept URLs from known file hosting services that contain file paths
        file_hosting_patterns = [
            'inaturalist-open-data.s3.amazonaws.com/photos/',
            'collections.nmnh.si.edu/media/?i=',
            'observation.org/photos/',
            'flickr.com/photos/'
        ]

        is_file_host = any(pattern in url_lower for pattern in file_hosting_patterns)

        # For iNaturalist, skip the webpage URLs (without file extension)
        if 'inaturalist.org/photos/' in url_lower and not has_file_extension:
            return False

        return has_file_extension or is_file_host
    
    def generate_filename(self, url: str, gbif_id: str = '', line_num: int = 0) -> str:
        """Generate appropriate filename for the download"""
        parsed_url = urlparse(url)
        
        # Get file extension from URL
        path = unquote(parsed_url.path)
        extension = Path(path).suffix
        
        if not extension:
            # Try to guess extension from URL or default to .jpg for images
            if 'image' in url.lower() or any(x in url.lower() for x in ['.jpg', '.jpeg', '.png', '.gif']):
                extension = '.jpg'
            else:
                extension = '.bin'
        
        # Generate base filename
        if gbif_id:
            base_name = f"gbif_{gbif_id}"
        else:
            # Use last part of URL path or generate from line number
            base_name = Path(path).stem
            if not base_name or len(base_name) < 3:
                base_name = f"download_{line_num:04d}"
        
        return f"{base_name}{extension}"
    
    def download_file(self, url: str, filename: str) -> bool:
        """
        Download single file with retry logic
        Returns True if successful, False otherwise
        """
        filepath = self.output_dir / filename
        
        # Skip if file already exists
        if filepath.exists():
            self.logger.info(f"File already exists, skipping: {filename}")
            self.stats['skipped'] += 1
            return True
        
        for attempt in range(self.retry_count):
            try:
                response = requests.get(
                    url, 
                    timeout=self.timeout,
                    headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
                )
                response.raise_for_status()
                
                # Write file
                with open(filepath, 'wb') as f:
                    f.write(response.content)
                
                self.logger.info(f"Downloaded: {filename} ({len(response.content)} bytes)")
                self.stats['success'] += 1
                
                # Add delay between downloads
                if self.delay_between_downloads > 0:
                    time.sleep(self.delay_between_downloads)
                
                return True
                
            except Exception as e:
                self.logger.warning(f"Attempt {attempt + 1} failed for {filename}: {e}")
                if attempt < self.retry_count - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff
        
        self.logger.error(f"Failed to download after {self.retry_count} attempts: {filename}")
        self.stats['failed'] += 1
        return False
    
    def download_all(self, use_parallel: bool = True) -> None:
        """Download all URLs"""
        urls = self.extract_urls_from_file()
        if not urls:
            self.logger.error("No valid URLs found!")
            return
        
        self.stats['total'] = len(urls)
        self.logger.info(f"Starting download of {len(urls)} files to {self.output_dir}")
        
        if use_parallel and self.max_workers > 1:
            self.download_parallel(urls)
        else:
            self.download_sequential(urls)
        
        self.print_summary()
    
    def download_parallel(self, urls: List[Tuple[str, str]]) -> None:
        """Download files in parallel using ThreadPoolExecutor"""
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all download tasks
            future_to_url = {
                executor.submit(self.download_file, url, filename): (url, filename)
                for url, filename in urls
            }
            
            # Process completed downloads with progress bar
            with tqdm(total=len(urls), desc="Downloading") as pbar:
                for future in as_completed(future_to_url):
                    url, filename = future_to_url[future]
                    try:
                        future.result()
                    except Exception as e:
                        self.logger.error(f"Unexpected error downloading {filename}: {e}")
                        self.stats['failed'] += 1
                    finally:
                        pbar.update(1)
    
    def download_sequential(self, urls: List[Tuple[str, str]]) -> None:
        """Download files sequentially with progress bar"""
        with tqdm(urls, desc="Downloading") as pbar:
            for url, filename in pbar:
                pbar.set_postfix_str(f"Current: {filename[:30]}...")
                self.download_file(url, filename)
    
    def print_summary(self) -> None:
        """Print download summary"""
        print("\n" + "="*50)
        print("DOWNLOAD SUMMARY")
        print("="*50)
        print(f"Total URLs: {self.stats['total']}")
        print(f"Successfully downloaded: {self.stats['success']}")
        print(f"Failed: {self.stats['failed']}")
        print(f"Skipped (already exists): {self.stats['skipped']}")

        # Calculate success rate safely
        if self.stats['total'] > 0:
            success_rate = (self.stats['success']/self.stats['total']*100)
            print(f"Success rate: {success_rate:.1f}%")
        else:
            print("Success rate: N/A")

        print(f"Files saved to: {self.output_dir.absolute()}")
        print("="*50)


def main():
    parser = argparse.ArgumentParser(description="URL Downloader Bot")
    parser.add_argument("input_file", help="File containing URLs to download")
    parser.add_argument("-o", "--output", default="downloads", help="Output directory (default: downloads)")
    parser.add_argument("-w", "--workers", type=int, default=5, help="Number of parallel workers (default: 5)")
    parser.add_argument("-t", "--timeout", type=int, default=30, help="Request timeout in seconds (default: 30)")
    parser.add_argument("-r", "--retry", type=int, default=3, help="Number of retry attempts (default: 3)")
    parser.add_argument("-d", "--delay", type=float, default=0.5, help="Delay between downloads in seconds (default: 0.5)")
    parser.add_argument("--sequential", action="store_true", help="Use sequential download instead of parallel")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.input_file):
        print(f"Error: Input file '{args.input_file}' not found!")
        sys.exit(1)
    
    # Create bot instance
    bot = URLDownloaderBot(
        input_file=args.input_file,
        output_dir=args.output,
        max_workers=args.workers,
        timeout=args.timeout,
        retry_count=args.retry,
        delay_between_downloads=args.delay
    )
    
    # Start downloading
    try:
        bot.download_all(use_parallel=not args.sequential)
    except KeyboardInterrupt:
        print("\nDownload interrupted by user!")
        bot.print_summary()
    except Exception as e:
        print(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
